import * as THREE from 'three';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass.js';
import { FilmPass } from 'three/examples/jsm/postprocessing/FilmPass.js';

// Custom shaders for military/war effects
const VignetteShader = {
  uniforms: {
    tDiffuse: { value: null },
    offset: { value: 1.0 },
    darkness: { value: 1.0 }
  },
  vertexShader: `
    varying vec2 vUv;
    void main() {
      vUv = uv;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,
  fragmentShader: `
    uniform sampler2D tDiffuse;
    uniform float offset;
    uniform float darkness;
    varying vec2 vUv;
    
    void main() {
      vec4 texel = texture2D(tDiffuse, vUv);
      vec2 uv = (vUv - vec2(0.5)) * vec2(offset);
      gl_FragColor = vec4(mix(texel.rgb, vec3(1.0 - darkness), dot(uv, uv)), texel.a);
    }
  `
};

const ScreenDistortionShader = {
  uniforms: {
    tDiffuse: { value: null },
    time: { value: 0.0 },
    distortion: { value: 0.1 },
    distortion2: { value: 0.05 },
    speed: { value: 0.3 },
    rollSpeed: { value: 0.1 }
  },
  vertexShader: `
    varying vec2 vUv;
    void main() {
      vUv = uv;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,
  fragmentShader: `
    uniform sampler2D tDiffuse;
    uniform float time;
    uniform float distortion;
    uniform float distortion2;
    uniform float speed;
    uniform float rollSpeed;
    varying vec2 vUv;
    
    vec3 mod289(vec3 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
    vec2 mod289(vec2 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
    vec3 permute(vec3 x) { return mod289(((x*34.0)+1.0)*x); }
    
    float snoise(vec2 v) {
      const vec4 C = vec4(0.211324865405187, 0.366025403784439, -0.577350269189626, 0.024390243902439);
      vec2 i = floor(v + dot(v, C.yy));
      vec2 x0 = v - i + dot(i, C.xx);
      vec2 i1 = (x0.x > x0.y) ? vec2(1.0, 0.0) : vec2(0.0, 1.0);
      vec4 x12 = x0.xyxy + C.xxzz;
      x12.xy -= i1;
      i = mod289(i);
      vec3 p = permute(permute(i.y + vec3(0.0, i1.y, 1.0)) + i.x + vec3(0.0, i1.x, 1.0));
      vec3 m = max(0.5 - vec3(dot(x0,x0), dot(x12.xy,x12.xy), dot(x12.zw,x12.zw)), 0.0);
      m = m*m;
      m = m*m;
      vec3 x = 2.0 * fract(p * C.www) - 1.0;
      vec3 h = abs(x) - 0.5;
      vec3 ox = floor(x + 0.5);
      vec3 a0 = x - ox;
      m *= 1.79284291400159 - 0.85373472095314 * (a0*a0 + h*h);
      vec3 g;
      g.x = a0.x * x0.x + h.x * x0.y;
      g.yz = a0.yz * x12.xz + h.yz * x12.yw;
      return 130.0 * dot(m, g);
    }
    
    void main() {
      vec2 uv = vUv;
      
      // Screen distortion
      float noise = snoise(uv * 10.0 + time * speed) * distortion;
      float noise2 = snoise(uv * 15.0 + time * speed * 1.5) * distortion2;
      
      uv.x += noise + noise2;
      uv.y += noise * 0.5;
      
      // Horizontal roll effect
      float roll = sin(time * rollSpeed) * 0.01;
      uv.y += roll;
      
      vec4 color = texture2D(tDiffuse, uv);
      
      // Add some color grading for military feel
      color.rgb = mix(color.rgb, color.rgb * vec3(0.9, 1.0, 0.8), 0.1);
      
      gl_FragColor = color;
    }
  `
};

const DamageOverlayShader = {
  uniforms: {
    tDiffuse: { value: null },
    damageAmount: { value: 0.0 },
    time: { value: 0.0 }
  },
  vertexShader: `
    varying vec2 vUv;
    void main() {
      vUv = uv;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,
  fragmentShader: `
    uniform sampler2D tDiffuse;
    uniform float damageAmount;
    uniform float time;
    varying vec2 vUv;
    
    void main() {
      vec4 color = texture2D(tDiffuse, vUv);
      
      // Red damage overlay
      float damage = damageAmount * (0.5 + 0.5 * sin(time * 10.0));
      vec3 damageColor = mix(color.rgb, vec3(1.0, 0.0, 0.0), damage * 0.3);
      
      // Vignette effect when damaged
      vec2 uv = vUv - 0.5;
      float vignette = 1.0 - dot(uv, uv) * damage * 2.0;
      damageColor *= vignette;
      
      gl_FragColor = vec4(damageColor, color.a);
    }
  `
};

export class PostProcessing {
  private composer: EffectComposer;
  private renderer: THREE.WebGLRenderer;
  private scene: THREE.Scene;
  private camera: THREE.Camera;
  
  // Passes
  private renderPass: RenderPass;
  private bloomPass: UnrealBloomPass;
  private filmPass: FilmPass;
  private vignettePass: ShaderPass;
  private distortionPass: ShaderPass;
  private damagePass: ShaderPass;
  
  // Effects state
  private damageAmount: number = 0;
  private distortionAmount: number = 0.02;
  
  constructor(renderer: THREE.WebGLRenderer, scene: THREE.Scene, camera: THREE.Camera) {
    this.renderer = renderer;
    this.scene = scene;
    this.camera = camera;
    
    this.composer = new EffectComposer(renderer);
    this.setupPasses();
  }
  
  private setupPasses() {
    // Basic render pass
    this.renderPass = new RenderPass(this.scene, this.camera);
    this.composer.addPass(this.renderPass);
    
    // Bloom effect for muzzle flashes and explosions
    this.bloomPass = new UnrealBloomPass(
      new THREE.Vector2(window.innerWidth, window.innerHeight),
      0.5, // strength
      0.4, // radius
      0.85 // threshold
    );
    this.composer.addPass(this.bloomPass);
    
    // Film grain for gritty war atmosphere
    this.filmPass = new FilmPass(
      0.35, // noise intensity
      0.025, // scanline intensity
      648, // scanline count
      false // grayscale
    );
    this.composer.addPass(this.filmPass);
    
    // Vignette effect
    this.vignettePass = new ShaderPass(VignetteShader);
    this.vignettePass.uniforms.offset.value = 0.95;
    this.vignettePass.uniforms.darkness.value = 0.6;
    this.composer.addPass(this.vignettePass);
    
    // Screen distortion for battle damage
    this.distortionPass = new ShaderPass(ScreenDistortionShader);
    this.distortionPass.uniforms.distortion.value = this.distortionAmount;
    this.composer.addPass(this.distortionPass);
    
    // Damage overlay
    this.damagePass = new ShaderPass(DamageOverlayShader);
    this.damagePass.renderToScreen = true;
    this.composer.addPass(this.damagePass);
  }
  
  update(deltaTime: number) {
    const time = performance.now() * 0.001;
    
    // Update shader uniforms
    this.distortionPass.uniforms.time.value = time;
    this.damagePass.uniforms.time.value = time;
    this.damagePass.uniforms.damageAmount.value = this.damageAmount;
    
    // Animate damage effect decay
    if (this.damageAmount > 0) {
      this.damageAmount = Math.max(0, this.damageAmount - deltaTime * 0.5);
    }
  }
  
  render() {
    this.composer.render();
  }
  
  resize(width: number, height: number) {
    this.composer.setSize(width, height);
    this.bloomPass.setSize(width, height);
  }
  
  // Effect triggers
  triggerDamageEffect(intensity: number = 0.8) {
    this.damageAmount = Math.min(1.0, this.damageAmount + intensity);
  }
  
  setDistortionAmount(amount: number) {
    this.distortionAmount = Math.max(0, Math.min(0.1, amount));
    this.distortionPass.uniforms.distortion.value = this.distortionAmount;
  }
  
  setBloomStrength(strength: number) {
    this.bloomPass.strength = Math.max(0, Math.min(3, strength));
  }
  
  setFilmIntensity(intensity: number) {
    this.filmPass.uniforms.nIntensity.value = Math.max(0, Math.min(1, intensity));
  }
  
  // Screen shake effect (to be called from camera controller)
  createScreenShake(intensity: number = 0.5, duration: number = 200) {
    const originalDistortion = this.distortionAmount;
    this.setDistortionAmount(originalDistortion + intensity * 0.05);
    
    setTimeout(() => {
      this.setDistortionAmount(originalDistortion);
    }, duration);
  }
  
  dispose() {
    this.composer.dispose();
  }
}
