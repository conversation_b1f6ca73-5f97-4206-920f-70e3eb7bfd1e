import * as THREE from 'three';
import { Player3D } from './Player3D';

export class CameraController {
  private camera: THREE.PerspectiveCamera;
  private player: Player3D;
  private originalPosition: THREE.Vector3;
  private originalRotation: THREE.Euler;
  
  // Camera shake
  private shakeIntensity: number = 0;
  private shakeDuration: number = 0;
  private shakeTime: number = 0;
  
  // Camera follow settings
  private followDistance: number = 20;
  private followHeight: number = 10;
  private followSmoothness: number = 0.05;
  private lookAheadDistance: number = 5;
  
  // Dynamic camera effects
  private recoilAmount: number = 0;
  private recoilDecay: number = 0.9;
  
  constructor(camera: THREE.PerspectiveCamera, player: Player3D) {
    this.camera = camera;
    this.player = player;
    this.originalPosition = camera.position.clone();
    this.originalRotation = camera.rotation.clone();
  }
  
  update(deltaTime: number) {
    this.updateCameraFollow(deltaTime);
    this.updateCameraShake(deltaTime);
    this.updateRecoilEffect(deltaTime);
    this.updateDynamicEffects(deltaTime);
  }
  
  private updateCameraFollow(deltaTime: number) {
    const playerPos = this.player.getPosition();
    const playerStats = this.player.getStats();
    
    // Calculate target position
    const targetX = playerPos.x;
    const targetY = this.followHeight;
    const targetZ = playerPos.z + this.followDistance;
    
    // Smooth camera movement
    this.camera.position.x += (targetX - this.camera.position.x) * this.followSmoothness;
    this.camera.position.y += (targetY - this.camera.position.y) * this.followSmoothness;
    this.camera.position.z += (targetZ - this.camera.position.z) * this.followSmoothness;
    
    // Look ahead of player movement
    const lookAtX = playerPos.x;
    const lookAtY = playerPos.y + 2;
    const lookAtZ = playerPos.z - this.lookAheadDistance;
    
    this.camera.lookAt(lookAtX, lookAtY, lookAtZ);
  }
  
  private updateCameraShake(deltaTime: number) {
    if (this.shakeDuration > 0) {
      this.shakeTime += deltaTime;
      
      // Calculate shake offset
      const shakeX = (Math.random() - 0.5) * this.shakeIntensity;
      const shakeY = (Math.random() - 0.5) * this.shakeIntensity;
      const shakeZ = (Math.random() - 0.5) * this.shakeIntensity * 0.5;
      
      // Apply shake to camera position
      this.camera.position.x += shakeX;
      this.camera.position.y += shakeY;
      this.camera.position.z += shakeZ;
      
      // Decay shake over time
      this.shakeDuration -= deltaTime;
      this.shakeIntensity *= 0.95;
      
      if (this.shakeDuration <= 0) {
        this.shakeDuration = 0;
        this.shakeIntensity = 0;
        this.shakeTime = 0;
      }
    }
  }
  
  private updateRecoilEffect(deltaTime: number) {
    if (this.recoilAmount > 0.01) {
      // Apply recoil to camera rotation
      this.camera.rotation.x += this.recoilAmount * 0.1;
      this.camera.position.z += this.recoilAmount * 0.5;
      
      // Decay recoil
      this.recoilAmount *= this.recoilDecay;
    } else {
      this.recoilAmount = 0;
    }
  }
  
  private updateDynamicEffects(deltaTime: number) {
    const playerStats = this.player.getStats();
    const healthRatio = playerStats.health / playerStats.maxHealth;
    
    // Camera sway when low health
    if (healthRatio < 0.3) {
      const swayIntensity = (0.3 - healthRatio) * 3;
      const time = Date.now() * 0.001;
      
      this.camera.rotation.z = Math.sin(time * 2) * swayIntensity * 0.02;
      this.camera.position.y += Math.sin(time * 1.5) * swayIntensity * 0.1;
    }
    
    // Breathing effect
    const breathingTime = Date.now() * 0.001;
    const breathingOffset = Math.sin(breathingTime * 0.5) * 0.05;
    this.camera.position.y += breathingOffset;
  }
  
  // Public methods for triggering effects
  triggerShake(intensity: number, duration: number) {
    this.shakeIntensity = Math.max(this.shakeIntensity, intensity);
    this.shakeDuration = Math.max(this.shakeDuration, duration);
  }
  
  triggerRecoil(amount: number) {
    this.recoilAmount += amount;
  }
  
  // Camera mode switching
  setCameraMode(mode: 'follow' | 'fixed' | 'firstPerson') {
    switch (mode) {
      case 'follow':
        this.followDistance = 20;
        this.followHeight = 10;
        break;
      case 'fixed':
        this.followDistance = 25;
        this.followHeight = 15;
        this.followSmoothness = 0.02;
        break;
      case 'firstPerson':
        this.followDistance = 0;
        this.followHeight = 2;
        this.followSmoothness = 0.1;
        break;
    }
  }
  
  // Cinematic camera movements
  createCinematicPan(startPos: THREE.Vector3, endPos: THREE.Vector3, duration: number) {
    const startTime = Date.now();
    
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / (duration * 1000), 1);
      
      // Smooth interpolation
      const t = this.easeInOutCubic(progress);
      this.camera.position.lerpVectors(startPos, endPos, t);
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    
    animate();
  }
  
  private easeInOutCubic(t: number): number {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }
  
  // Zoom effects
  setFieldOfView(fov: number, duration: number = 0) {
    if (duration === 0) {
      this.camera.fov = fov;
      this.camera.updateProjectionMatrix();
    } else {
      const startFov = this.camera.fov;
      const startTime = Date.now();
      
      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / (duration * 1000), 1);
        
        this.camera.fov = startFov + (fov - startFov) * progress;
        this.camera.updateProjectionMatrix();
        
        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };
      
      animate();
    }
  }
  
  // Slow motion effect
  createSlowMotionEffect(duration: number, timeScale: number = 0.3) {
    // This would need to be integrated with the game engine's time system
    console.log(`Slow motion effect: ${duration}s at ${timeScale}x speed`);
  }
  
  // Death cam effect
  createDeathCam() {
    const playerPos = this.player.getPosition();
    
    // Move camera to dramatic angle
    const deathCamPos = new THREE.Vector3(
      playerPos.x + 5,
      playerPos.y + 8,
      playerPos.z + 10
    );
    
    this.createCinematicPan(this.camera.position.clone(), deathCamPos, 2);
    
    // Look at player
    setTimeout(() => {
      this.camera.lookAt(playerPos);
    }, 1000);
  }
  
  // Reset camera to default state
  reset() {
    this.shakeIntensity = 0;
    this.shakeDuration = 0;
    this.shakeTime = 0;
    this.recoilAmount = 0;
    
    this.camera.position.copy(this.originalPosition);
    this.camera.rotation.copy(this.originalRotation);
    this.camera.fov = 75;
    this.camera.updateProjectionMatrix();
  }
  
  // Get camera state for UI elements
  getCameraState() {
    return {
      position: this.camera.position.clone(),
      rotation: this.camera.rotation.clone(),
      fov: this.camera.fov,
      isShaking: this.shakeDuration > 0,
      shakeIntensity: this.shakeIntensity
    };
  }
}
