import * as THREE from 'three';
import { Enemy3D } from './Enemy3D';

export class Bullet3D {
  private scene: THREE.Scene;
  private mesh: THREE.Mesh;
  private position: THREE.Vector3;
  private direction: THREE.Vector3;
  private speed: number = 50;
  private active: boolean = true;
  private maxDistance: number = 100;
  private traveledDistance: number = 0;
  
  // Visual effects
  private trail: THREE.Line;
  private trailPositions: THREE.Vector3[] = [];
  private maxTrailLength: number = 10;
  
  constructor(scene: THREE.Scene, startPosition: THREE.Vector3, direction: THREE.Vector3) {
    this.scene = scene;
    this.position = startPosition.clone();
    this.direction = direction.normalize();
    
    this.createBulletMesh();
    this.createTrail();
  }
  
  private createBulletMesh() {
    // Create bullet geometry (small cylinder)
    const geometry = new THREE.CylinderGeometry(0.02, 0.02, 0.2, 8);
    const material = new THREE.MeshBasicMaterial({ 
      color: 0xffff00,
      emissive: 0xffff00,
      emissiveIntensity: 0.5
    });
    
    this.mesh = new THREE.Mesh(geometry, material);
    this.mesh.position.copy(this.position);
    
    // Rotate bullet to face direction
    this.mesh.lookAt(this.position.clone().add(this.direction));
    this.mesh.rotateX(Math.PI / 2);
    
    this.scene.add(this.mesh);
  }
  
  private createTrail() {
    // Create trail geometry
    const geometry = new THREE.BufferGeometry();
    const material = new THREE.LineBasicMaterial({ 
      color: 0xffff00,
      transparent: true,
      opacity: 0.6
    });
    
    // Initialize positions array
    const positions = new Float32Array(this.maxTrailLength * 3);
    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    
    this.trail = new THREE.Line(geometry, material);
    this.scene.add(this.trail);
  }
  
  update(deltaTime: number) {
    if (!this.active) return;
    
    // Move bullet
    const movement = this.direction.clone().multiplyScalar(this.speed * deltaTime);
    this.position.add(movement);
    this.traveledDistance += movement.length();
    
    // Update mesh position
    this.mesh.position.copy(this.position);
    
    // Update trail
    this.updateTrail();
    
    // Check if bullet should be destroyed
    if (this.traveledDistance > this.maxDistance || this.position.y < -10) {
      this.destroy();
    }
  }
  
  private updateTrail() {
    // Add current position to trail
    this.trailPositions.unshift(this.position.clone());
    
    // Limit trail length
    if (this.trailPositions.length > this.maxTrailLength) {
      this.trailPositions.pop();
    }
    
    // Update trail geometry
    const positions = this.trail.geometry.attributes.position.array as Float32Array;
    
    for (let i = 0; i < this.maxTrailLength; i++) {
      if (i < this.trailPositions.length) {
        const pos = this.trailPositions[i];
        positions[i * 3] = pos.x;
        positions[i * 3 + 1] = pos.y;
        positions[i * 3 + 2] = pos.z;
      } else {
        // Fill remaining positions with the last valid position
        const lastPos = this.trailPositions[this.trailPositions.length - 1] || this.position;
        positions[i * 3] = lastPos.x;
        positions[i * 3 + 1] = lastPos.y;
        positions[i * 3 + 2] = lastPos.z;
      }
    }
    
    this.trail.geometry.attributes.position.needsUpdate = true;
    
    // Fade trail opacity based on age
    const material = this.trail.material as THREE.LineBasicMaterial;
    material.opacity = Math.max(0.1, 0.6 * (1 - this.traveledDistance / this.maxDistance));
  }
  
  checkCollision(enemy: Enemy3D): boolean {
    if (!this.active || !enemy.isActive()) return false;
    
    const enemyPosition = enemy.getPosition();
    const distance = this.position.distanceTo(enemyPosition);
    
    // Simple sphere collision detection
    const collisionRadius = 1.5; // Adjust based on enemy size
    return distance < collisionRadius;
  }
  
  destroy() {
    if (!this.active) return;
    
    this.active = false;
    
    // Remove from scene
    this.scene.remove(this.mesh);
    this.scene.remove(this.trail);
    
    // Dispose of geometries and materials
    this.mesh.geometry.dispose();
    (this.mesh.material as THREE.Material).dispose();
    this.trail.geometry.dispose();
    (this.trail.material as THREE.Material).dispose();
  }
  
  isActive(): boolean {
    return this.active;
  }
  
  getPosition(): THREE.Vector3 {
    return this.position.clone();
  }
}
