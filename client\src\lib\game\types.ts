export interface Vector2 {
  x: number;
  y: number;
}

export interface GameObject {
  position: Vector2;
  velocity: Vector2;
  width: number;
  height: number;
  active: boolean;
}

export interface PlayerStats {
  health: number;
  maxHealth: number;
  weaponType: WeaponType;
  fireRate: number;
  lastShotTime: number;
}

export enum WeaponType {
  NORMAL = 'normal',
  SPREAD = 'spread',
  RAPID = 'rapid',
  LASER = 'laser'
}

export enum EnemyType {
  SOLDIER = 'soldier',
  TANK = 'tank',
  HELICOPTER = 'helicopter'
}

export enum PowerUpType {
  WEAPON_UPGRADE = 'weapon',
  HEALTH = 'health',
  EXTRA_LIFE = 'life',
  SCORE_BONUS = 'score'
}

export interface BulletData extends GameObject {
  damage: number;
  isPlayerBullet: boolean;
  id: string;
}

export interface EnemyData extends GameObject {
  type: EnemyType;
  health: number;
  maxHealth: number;
  lastShotTime: number;
  fireRate: number;
  id: string;
}

export interface PowerUpData extends GameObject {
  type: PowerUpType;
  value: number;
  id: string;
}
