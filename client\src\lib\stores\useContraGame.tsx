import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

export type GameState = 'menu' | 'playing' | 'gameOver';

interface ContraGameState {
  gameState: GameState;
  score: number;
  lives: number;
  highScore: number;
  level: number;
  
  // Actions
  setGameState: (state: GameState) => void;
  startGame: () => void;
  restartGame: () => void;
  returnToMenu: () => void;
  addScore: (points: number) => void;
  loseLife: () => void;
  nextLevel: () => void;
}

export const useContraGame = create<ContraGameState>()(
  subscribeWithSelector((set, get) => ({
    gameState: 'menu',
    score: 0,
    lives: 3,
    highScore: parseInt(localStorage.getItem('contraHighScore') || '0'),
    level: 1,
    
    setGameState: (state) => set({ gameState: state }),
    
    startGame: () => {
      set({
        gameState: 'playing',
        score: 0,
        lives: 3,
        level: 1
      });
    },
    
    restartGame: () => {
      set({
        gameState: 'playing',
        score: 0,
        lives: 3,
        level: 1
      });
    },
    
    returnToMenu: () => {
      set({ gameState: 'menu' });
    },
    
    addScore: (points) => {
      console.log('addScore called with points:', points);
      const newScore = get().score + points;
      const currentHighScore = get().highScore;
      
      console.log('Current score:', get().score, 'New score:', newScore);
      
      if (newScore > currentHighScore) {
        localStorage.setItem('contraHighScore', newScore.toString());
        set({ score: newScore, highScore: newScore });
      } else {
        set({ score: newScore });
      }
    },
    
    loseLife: () => {
      const newLives = get().lives - 1;
      set({ lives: newLives });
      
      if (newLives <= 0) {
        set({ gameState: 'gameOver' });
      }
    },
    
    nextLevel: () => {
      set({ level: get().level + 1 });
    }
  }))
);
