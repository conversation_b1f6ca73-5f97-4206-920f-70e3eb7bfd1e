import { useEffect, useState } from 'react';
import { useContraGame } from '../../lib/stores/useContraGame';

const GameUI = () => {
  const { gameState, score, lives, restartGame, returnToMenu } = useContraGame();
  const [glitchActive, setGlitchActive] = useState(false);

  // Glitch effect for game over screen
  useEffect(() => {
    if (gameState === 'gameOver') {
      const glitchInterval = setInterval(() => {
        if (Math.random() < 0.3) {
          setGlitchActive(true);
          setTimeout(() => setGlitchActive(false), 100);
        }
      }, 1000);

      return () => clearInterval(glitchInterval);
    }
  }, [gameState]);

  if (gameState === 'gameOver') {
    return (
      <div className="absolute inset-0 flex items-center justify-center bg-black/80 backdrop-blur-sm">
        {/* Background effects */}
        <div className="absolute inset-0">
          <div
            className="absolute inset-0 opacity-5"
            style={{
              backgroundImage: `
                linear-gradient(rgba(255, 0, 0, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 0, 0, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }}
          />
        </div>

        <div className="relative text-center space-y-8 p-12 bg-gradient-to-br from-gray-900/90 to-black/90 border-2 border-red-500 backdrop-blur-md max-w-lg">
          {/* Corner decorations */}
          <div className="absolute top-2 left-2 w-8 h-8 border-l-2 border-t-2 border-red-500" />
          <div className="absolute top-2 right-2 w-8 h-8 border-r-2 border-t-2 border-red-500" />
          <div className="absolute bottom-2 left-2 w-8 h-8 border-l-2 border-b-2 border-red-500" />
          <div className="absolute bottom-2 right-2 w-8 h-8 border-r-2 border-b-2 border-red-500" />

          {/* Game Over Title */}
          <div className="relative">
            <h2
              className={`text-6xl font-black text-red-500 font-mono tracking-wider ${
                glitchActive ? 'animate-glitch' : ''
              }`}
              style={{
                textShadow: glitchActive
                  ? '2px 0 #ff0000, -2px 0 #00ff00'
                  : '0 0 20px rgba(255, 0, 0, 0.8)'
              }}
            >
              MISSION FAILED
            </h2>

            {/* Glitch overlay */}
            {glitchActive && (
              <h2
                className="absolute top-0 left-0 text-6xl font-black text-green-500 font-mono tracking-wider opacity-70"
                style={{
                  transform: 'translate(-2px, 1px)',
                  clipPath: 'polygon(0 0, 100% 0, 100% 60%, 0 60%)'
                }}
              >
                MISSION FAILED
              </h2>
            )}
          </div>

          {/* Score display */}
          <div className="bg-black/50 border border-red-500/50 p-6 rounded">
            <p className="text-red-400 font-mono text-sm uppercase tracking-wider mb-2">Final Score</p>
            <p className="text-4xl font-bold text-white font-mono">{score.toLocaleString()}</p>
          </div>

          {/* Action buttons */}
          <div className="space-y-4">
            <button
              onClick={restartGame}
              className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white font-bold py-4 px-8 font-mono uppercase tracking-wider transition-all duration-300 transform hover:scale-105 border border-green-400"
              style={{
                clipPath: 'polygon(8px 0%, 100% 0%, calc(100% - 8px) 100%, 0% 100%)',
                boxShadow: '0 0 20px rgba(0, 255, 0, 0.3)'
              }}
            >
              ▶ Retry Mission
            </button>

            <button
              onClick={returnToMenu}
              className="w-full bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-green-400 font-bold py-4 px-8 font-mono uppercase tracking-wider transition-all duration-300 transform hover:scale-105 border border-green-500"
              style={{
                clipPath: 'polygon(8px 0%, 100% 0%, calc(100% - 8px) 100%, 0% 100%)'
              }}
            >
              ← Return to Base
            </button>
          </div>

          {/* Status message */}
          <div className="text-red-400 font-mono text-sm uppercase tracking-wider opacity-75">
            Combat Unit Eliminated
          </div>
        </div>
      </div>
    );
  }

  // In-game HUD
  return (
    <div className="absolute top-4 left-4 z-10">
      <div className="bg-black/70 border-2 border-green-500 p-4 backdrop-blur-sm font-mono">
        {/* HUD Header */}
        <div className="text-green-400 text-xs uppercase tracking-wider mb-2 border-b border-green-500/30 pb-1">
          Combat Status
        </div>

        <div className="space-y-2">
          {/* Score */}
          <div className="flex justify-between items-center">
            <span className="text-green-400 text-sm">SCORE:</span>
            <span className="text-white font-bold text-lg">{score.toLocaleString()}</span>
          </div>

          {/* Lives */}
          <div className="flex justify-between items-center">
            <span className="text-green-400 text-sm">LIVES:</span>
            <div className="flex space-x-1">
              {Array.from({ length: lives }, (_, i) => (
                <div key={i} className="w-3 h-3 bg-red-500 border border-red-400" />
              ))}
              {Array.from({ length: Math.max(0, 3 - lives) }, (_, i) => (
                <div key={i + lives} className="w-3 h-3 bg-gray-700 border border-gray-600" />
              ))}
            </div>
          </div>
        </div>

        {/* Status indicator */}
        <div className="mt-3 pt-2 border-t border-green-500/30">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            <span className="text-green-400 text-xs uppercase">Active</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GameUI;
