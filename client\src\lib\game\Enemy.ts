import { EnemyData, Vector2, EnemyType } from './types';
import { useAudio } from '../stores/useAudio';

export class Enemy implements EnemyData {
  position: Vector2;
  velocity: Vector2;
  width: number;
  height: number;
  active: boolean = true;
  type: EnemyType;
  health: number;
  maxHealth: number;
  lastShotTime: number = 0;
  fireRate: number;
  id: string;
  
  private speed: number;
  private canvasWidth: number;
  private canvasHeight: number;
  private movePattern: string = 'straight';
  private spawnTime: number;

  constructor(
    type: EnemyType,
    startPosition: Vector2,
    canvasWidth: number,
    canvasHeight: number
  ) {
    this.type = type;
    this.position = { ...startPosition };
    this.canvasWidth = canvasWidth;
    this.canvasHeight = canvasHeight;
    this.spawnTime = Date.now();
    this.id = Math.random().toString(36).substr(2, 9);

    // Set enemy properties based on type
    this.setupEnemyType();
    
    this.velocity = { x: -this.speed, y: 0 };
  }

  private setupEnemyType() {
    switch (this.type) {
      case EnemyType.SOLDIER:
        this.width = 20; // Much smaller for easier gameplay
        this.height = 30; // Much smaller for easier gameplay
        this.speed = 80;
        this.health = 50;
        this.maxHealth = 50;
        this.fireRate = 2000;
        this.movePattern = 'straight';
        break;
        
      case EnemyType.TANK:
        this.width = 45; // Smaller tank for better gameplay
        this.height = 30; // Smaller tank for better gameplay
        this.speed = 40;
        this.health = 150;
        this.maxHealth = 150;
        this.fireRate = 3000;
        this.movePattern = 'straight';
        break;
        
      case EnemyType.HELICOPTER:
        this.width = 40; // Much smaller helicopter for better gameplay
        this.height = 25; // Much smaller helicopter for better gameplay
        this.speed = 120;
        this.health = 75;
        this.maxHealth = 75;
        this.fireRate = 1500;
        this.movePattern = 'sine';
        break;
    }
  }

  update(deltaTime: number) {
    if (!this.active) return;

    this.updateMovement(deltaTime);
    this.updatePosition(deltaTime);
    this.checkBounds();
  }

  private updateMovement(deltaTime: number) {
    switch (this.movePattern) {
      case 'sine':
        const time = (Date.now() - this.spawnTime) / 1000;
        this.velocity.y = Math.sin(time * 2) * 50;
        break;
        
      case 'straight':
      default:
        this.velocity.y = 0;
        break;
    }
  }

  private updatePosition(deltaTime: number) {
    this.position.x += this.velocity.x * deltaTime;
    this.position.y += this.velocity.y * deltaTime;

    // Keep helicopters within vertical bounds
    if (this.type === EnemyType.HELICOPTER) {
      this.position.y = Math.max(50, Math.min(this.canvasHeight - this.height - 50, this.position.y));
    }
  }

  private checkBounds() {
    if (this.position.x + this.width < 0) {
      this.active = false;
    }
  }

  canShoot(): boolean {
    const now = Date.now();
    return now - this.lastShotTime >= this.fireRate;
  }

  shoot(): Vector2 | null {
    if (!this.canShoot()) return null;

    this.lastShotTime = Date.now();

    // Play enemy shooting sound
    const audioStore = useAudio.getState();
    if (audioStore.hitSound && !audioStore.isMuted) {
      const enemyShootSound = audioStore.hitSound.cloneNode() as HTMLAudioElement;
      enemyShootSound.volume = 0.15;
      enemyShootSound.playbackRate = 0.8; // Lower pitch for enemy guns
      enemyShootSound.play().catch(console.log);
    }

    return {
      x: this.position.x,
      y: this.position.y + this.height / 2
    };
  }

  takeDamage(damage: number): boolean {
    this.health -= damage;

    if (this.health <= 0) {
      this.health = 0;
      this.active = false;

      // Play enemy death sound
      const audioStore = useAudio.getState();
      if (audioStore.successSound && !audioStore.isMuted) {
        const deathSound = audioStore.successSound.cloneNode() as HTMLAudioElement;
        deathSound.volume = 0.3;
        deathSound.playbackRate = 0.7; // Lower pitch for death sound
        deathSound.play().catch(console.log);
      }

      return true; // Enemy destroyed
    }

    // Play enemy hit sound
    const audioStore = useAudio.getState();
    if (audioStore.hitSound && !audioStore.isMuted) {
      const hitSound = audioStore.hitSound.cloneNode() as HTMLAudioElement;
      hitSound.volume = 0.1;
      hitSound.playbackRate = 1.2;
      hitSound.play().catch(console.log);
    }

    return false;
  }

  render(ctx: CanvasRenderingContext2D) {
    if (!this.active) return;

    // Draw enemy based on type
    switch (this.type) {
      case EnemyType.SOLDIER:
        this.renderSoldier(ctx);
        break;
      case EnemyType.TANK:
        this.renderTank(ctx);
        break;
      case EnemyType.HELICOPTER:
        this.renderHelicopter(ctx);
        break;
    }

    // Draw health bar for stronger enemies
    if (this.maxHealth > 50) {
      this.renderHealthBar(ctx);
    }
  }

  private renderSoldier(ctx: CanvasRenderingContext2D) {
    const x = this.position.x;
    const y = this.position.y;
    const w = this.width;
    const h = this.height;
    const centerX = x + w / 2;
    const centerY = y + h / 2;

    ctx.save();

    // MASSIVE 3D SHADOW for dramatic effect
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.beginPath();
    ctx.ellipse(centerX + 8, y + h + 10, w * 0.7, 15, 0, 0, Math.PI * 2);
    ctx.fill();

    // BRIGHT RED ENEMY OUTLINE for maximum visibility
    ctx.strokeStyle = '#FF0000'; // Bright red outline
    ctx.lineWidth = 3;
    ctx.strokeRect(x - 2, y - 2, w + 4, h + 4);

    // Secondary dark outline for menacing look
    ctx.strokeStyle = '#8B0000';
    ctx.lineWidth = 2;
    ctx.strokeRect(x, y, w, h);

    // 3D ENEMY BODY with realistic gradient shading (EVIL RED UNIFORM)
    const bodyGradient = ctx.createLinearGradient(x, y + 20, x + w, y + h - 20);
    bodyGradient.addColorStop(0, '#4A0000'); // Very dark red
    bodyGradient.addColorStop(0.2, '#8B0000'); // Dark red
    bodyGradient.addColorStop(0.5, '#CD5C5C'); // Medium red
    bodyGradient.addColorStop(0.8, '#DC143C'); // Crimson
    bodyGradient.addColorStop(1, '#8B0000'); // Back to dark for depth

    ctx.fillStyle = bodyGradient;
    ctx.fillRect(x + 6, y + 25, w - 12, h - 40);

    // Add enemy armor/vest with menacing 3D effect
    const armorGradient = ctx.createLinearGradient(x + 8, y + 30, x + w - 8, y + 55);
    armorGradient.addColorStop(0, '#1F1F1F'); // Very dark
    armorGradient.addColorStop(0.5, '#2F2F2F'); // Dark gray
    armorGradient.addColorStop(1, '#0F0F0F'); // Almost black
    ctx.fillStyle = armorGradient;
    ctx.fillRect(x + 8, y + 30, w - 16, 25);

    // MENACING 3D HEAD with evil shading
    const headGradient = ctx.createRadialGradient(centerX - 4, y + 18, 2, centerX, y + 18, 18);
    headGradient.addColorStop(0, '#FFDBAC'); // Light skin
    headGradient.addColorStop(0.5, '#D4A574'); // Medium skin
    headGradient.addColorStop(1, '#8B7355'); // Dark skin for evil look

    ctx.fillStyle = headGradient;
    ctx.beginPath();
    ctx.arc(centerX, y + 18, 18, 0, Math.PI * 2);
    ctx.fill();

    // EVIL TACTICAL HELMET with realistic 3D effect
    const helmetGradient = ctx.createLinearGradient(centerX - 20, y - 2, centerX + 20, y + 25);
    helmetGradient.addColorStop(0, '#0F0F0F'); // Almost black
    helmetGradient.addColorStop(0.3, '#1F1F1F'); // Very dark
    helmetGradient.addColorStop(0.7, '#2F2F2F'); // Dark gray
    helmetGradient.addColorStop(1, '#0F0F0F'); // Back to almost black

    ctx.fillStyle = helmetGradient;
    ctx.beginPath();
    ctx.arc(centerX, y + 12, 20, Math.PI, Math.PI * 2);
    ctx.fill();

    // Helmet details and evil accessories
    ctx.fillStyle = '#8B0000'; // Dark red straps
    ctx.fillRect(centerX - 22, y + 15, 44, 3); // Chin strap
    ctx.fillStyle = '#FF0000'; // Red evil visor
    ctx.fillRect(centerX - 3, y + 5, 6, 8); // Night vision/targeting system

    // MENACING GLOWING RED EYES
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(centerX - 8, y + 15, 5, 4);
    ctx.fillRect(centerX + 3, y + 15, 5, 4);
    ctx.fillStyle = '#FF0000'; // Glowing red eyes
    ctx.fillRect(centerX - 7, y + 16, 3, 3);
    ctx.fillRect(centerX + 4, y + 16, 3, 3);

    // Add evil glow effect around eyes
    const eyeGlow = ctx.createRadialGradient(centerX, y + 17, 0, centerX, y + 17, 12);
    eyeGlow.addColorStop(0, 'rgba(255, 0, 0, 0.8)');
    eyeGlow.addColorStop(1, 'rgba(255, 0, 0, 0)');
    ctx.fillStyle = eyeGlow;
    ctx.beginPath();
    ctx.arc(centerX, y + 17, 12, 0, Math.PI * 2);
    ctx.fill();

    // Evil scar and facial features
    ctx.fillStyle = '#8B0000';
    ctx.fillRect(centerX - 1, y + 20, 2, 4); // Nose
    ctx.fillRect(centerX - 3, y + 25, 6, 1); // Evil grimace

    // POWERFUL MUSCULAR ARMS with 3D shading
    const armGradient = ctx.createLinearGradient(x, y + 25, x + 18, y + 55);
    armGradient.addColorStop(0, '#CD5C5C');
    armGradient.addColorStop(0.5, '#8B0000');
    armGradient.addColorStop(1, '#4A0000');

    ctx.fillStyle = armGradient;
    ctx.fillRect(x - 3, y + 25, 15, 30); // Left arm (much bigger)
    ctx.fillRect(x + w - 12, y + 25, 15, 30); // Right arm (much bigger)

    // TACTICAL GLOVES with evil details
    ctx.fillStyle = '#1F1F1F'; // Black tactical gloves
    ctx.fillRect(x - 3, y + 50, 12, 15); // Left hand
    ctx.fillRect(x + w - 9, y + 50, 12, 15); // Right hand

    // Glove details
    ctx.fillStyle = '#8B0000';
    ctx.fillRect(x - 1, y + 52, 8, 1); // Left glove strap
    ctx.fillRect(x + w - 7, y + 52, 8, 1); // Right glove strap

    // POWERFUL LEGS with muscle definition
    const legGradient = ctx.createLinearGradient(x + 10, y + h - 30, x + w - 10, y + h);
    legGradient.addColorStop(0, '#CD5C5C');
    legGradient.addColorStop(0.5, '#8B0000');
    legGradient.addColorStop(1, '#4A0000');

    ctx.fillStyle = legGradient;
    ctx.fillRect(x + 10, y + h - 30, 15, 30); // Left leg (much bigger)
    ctx.fillRect(x + w - 25, y + h - 30, 15, 30); // Right leg (much bigger)

    // COMBAT BOOTS with menacing detail
    const bootGradient = ctx.createLinearGradient(x + 8, y + h - 15, x + w - 8, y + h);
    bootGradient.addColorStop(0, '#0F0F0F');
    bootGradient.addColorStop(0.5, '#1F1F1F');
    bootGradient.addColorStop(1, '#000000');

    ctx.fillStyle = bootGradient;
    ctx.fillRect(x + 8, y + h - 15, 20, 15); // Left boot (much bigger)
    ctx.fillRect(x + w - 28, y + h - 15, 20, 15); // Right boot (much bigger)

    // Boot spikes/studs for evil look
    ctx.fillStyle = '#2F2F2F';
    for (let i = 0; i < 3; i++) {
      ctx.fillRect(x + 10 + i * 5, y + h - 8, 2, 2); // Left boot studs
      ctx.fillRect(x + w - 26 + i * 5, y + h - 8, 2, 2); // Right boot studs
    }

    // MASSIVE EVIL ASSAULT RIFLE with 3D details
    const weaponGradient = ctx.createLinearGradient(x - 15, y + 35, x + 20, y + 45);
    weaponGradient.addColorStop(0, '#1F1F1F'); // Very dark metal
    weaponGradient.addColorStop(0.3, '#2F2F2F'); // Dark metal
    weaponGradient.addColorStop(0.7, '#4F4F4F'); // Medium metal
    weaponGradient.addColorStop(1, '#0F0F0F'); // Almost black for depth

    // Main rifle body (pointing left toward player)
    ctx.fillStyle = weaponGradient;
    ctx.fillRect(x - 18, y + 33, 35, 10); // Main body (much longer)

    // Rifle barrel with realistic thickness
    ctx.fillStyle = '#0A0A0A';
    ctx.fillRect(x - 25, y + 36, 25, 5); // Long barrel

    // Rifle stock
    ctx.fillStyle = '#4A0000'; // Dark red stock to match uniform
    ctx.fillRect(x + 12, y + 30, 18, 15);

    // Advanced scope/sight
    ctx.fillStyle = '#1A1A1A';
    ctx.fillRect(x - 10, y + 28, 15, 5);
    ctx.fillStyle = '#FF0000'; // Red targeting laser
    ctx.fillRect(x - 8, y + 29, 2, 2);
    ctx.fillRect(x - 3, y + 29, 2, 2);

    // Trigger guard
    ctx.strokeStyle = '#1F1F1F';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.arc(x + 8, y + 40, 4, 0, Math.PI);
    ctx.stroke();

    // Large magazine
    ctx.fillStyle = '#0F0F0F';
    ctx.fillRect(x + 5, y + 43, 10, 20);

    // Weapon attachments (grenade launcher)
    ctx.fillStyle = '#2F2F2F';
    ctx.fillRect(x - 5, y + 45, 12, 6);

    // Muzzle flash effect (if recently fired)
    const timeSinceShot = Date.now() - this.lastShotTime;
    if (timeSinceShot < 150) {
      const flashGradient = ctx.createRadialGradient(x - 25, y + 38, 0, x - 25, y + 38, 20);
      flashGradient.addColorStop(0, '#FFFF00'); // Bright yellow center
      flashGradient.addColorStop(0.2, '#FF8C00'); // Orange
      flashGradient.addColorStop(0.5, '#FF4500'); // Red-orange
      flashGradient.addColorStop(0.8, '#8B0000'); // Dark red
      flashGradient.addColorStop(1, 'rgba(139, 0, 0, 0)'); // Transparent edge

      ctx.fillStyle = flashGradient;
      ctx.beginPath();
      ctx.arc(x - 25, y + 38, 20, 0, Math.PI * 2);
      ctx.fill();
    }

    // Add menacing movement animation
    const time = Date.now() / 100;
    const isMoving = this.velocity.x !== 0 || this.velocity.y !== 0;
    if (isMoving) {
      const bobOffset = Math.sin(time) * 3; // More aggressive movement
      const sway = Math.cos(time * 0.8) * 2;
      ctx.translate(sway, bobOffset);
    }

    ctx.restore();
  }

  private renderTank(ctx: CanvasRenderingContext2D) {
    const x = this.position.x;
    const y = this.position.y;
    const w = this.width;
    const h = this.height;

    ctx.save();

    // Draw shadow for better visibility
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.fillRect(x + 3, y + h + 2, w, 6);

    // Tank treads (bottom)
    ctx.fillStyle = '#1C1C1C';
    ctx.fillRect(x, y + h - 10, w, 10);

    // Tread details (wheels)
    ctx.fillStyle = '#333333';
    for (let i = 0; i < 4; i++) {
      const wheelX = x + 5 + (i * 12);
      ctx.beginPath();
      ctx.arc(wheelX, y + h - 5, 4, 0, Math.PI * 2);
      ctx.fill();
    }

    // Main tank body
    ctx.fillStyle = '#2F4F2F'; // Dark green
    ctx.fillRect(x + 5, y + 8, w - 10, h - 18);

    // Tank turret
    ctx.fillStyle = '#556B2F'; // Olive green
    ctx.beginPath();
    ctx.arc(x + w/2, y + 15, 12, 0, Math.PI * 2);
    ctx.fill();

    // Tank cannon (long barrel)
    ctx.fillStyle = '#2F2F2F';
    ctx.fillRect(x - 20, y + 13, 25, 4);
    
    // Cannon tip
    ctx.fillStyle = '#1C1C1C';
    ctx.fillRect(x - 22, y + 14, 3, 2);

    // Tank armor details
    ctx.fillStyle = '#8FBC8F'; // Light green highlights
    ctx.fillRect(x + 8, y + 12, w - 16, 2);
    ctx.fillRect(x + 8, y + 20, w - 16, 2);

    // Hatch
    ctx.fillStyle = '#2F2F2F';
    ctx.beginPath();
    ctx.arc(x + w/2 + 8, y + 12, 3, 0, Math.PI * 2);
    ctx.fill();

    // Side armor plates
    ctx.strokeStyle = '#1C1C1C';
    ctx.lineWidth = 1;
    ctx.strokeRect(x + 6, y + 10, w - 12, h - 20);

    ctx.restore();
  }

  private renderHelicopter(ctx: CanvasRenderingContext2D) {
    const x = this.position.x;
    const y = this.position.y;
    const w = this.width;
    const h = this.height;

    ctx.save();

    // Draw shadow for better visibility
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.ellipse(x + w/2 + 2, y + h + 8, w * 0.6, 6, 0, 0, Math.PI * 2);
    ctx.fill();

    // Main helicopter body (fuselage)
    ctx.fillStyle = '#2F4F4F'; // Dark slate gray
    ctx.beginPath();
    ctx.ellipse(x + w/2, y + h/2 + 3, w/2 - 2, h/2 - 3, 0, 0, Math.PI * 2);
    ctx.fill();

    // Cockpit (glass)
    ctx.fillStyle = '#87CEEB'; // Sky blue (reflective)
    ctx.beginPath();
    ctx.ellipse(x + w/2 - 5, y + h/2, 12, 8, 0, 0, Math.PI * 2);
    ctx.fill();

    // Cockpit frame
    ctx.strokeStyle = '#2F2F2F';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.ellipse(x + w/2 - 5, y + h/2, 12, 8, 0, 0, Math.PI * 2);
    ctx.stroke();

    // Landing skids
    ctx.fillStyle = '#1C1C1C';
    ctx.fillRect(x + 5, y + h - 3, w - 10, 2);
    ctx.fillRect(x + 8, y + h - 6, 4, 4); // Left skid support
    ctx.fillRect(x + w - 12, y + h - 6, 4, 4); // Right skid support

    // Tail boom
    ctx.fillStyle = '#2F4F4F';
    ctx.fillRect(x + w - 8, y + h/2 - 2, 15, 4);

    // Tail rotor
    const tailTime = Date.now() / 50;
    ctx.save();
    ctx.translate(x + w + 5, y + h/2);
    ctx.rotate(tailTime);
    ctx.fillStyle = '#888888';
    ctx.fillRect(-8, -1, 16, 2);
    ctx.fillRect(-1, -8, 2, 16);
    ctx.restore();

    // Main rotor (animated)
    const time = Date.now() / 80;
    ctx.save();
    ctx.translate(x + w/2, y + 3);
    ctx.rotate(time);
    ctx.fillStyle = '#666666';
    // Main rotor blades
    ctx.fillRect(-w/2 - 5, -1, w + 10, 2);
    ctx.fillRect(-1, -w/2 - 5, 2, w + 10);
    ctx.restore();

    // Rotor hub
    ctx.fillStyle = '#2F2F2F';
    ctx.beginPath();
    ctx.arc(x + w/2, y + 3, 3, 0, Math.PI * 2);
    ctx.fill();

    // Engine exhaust
    ctx.fillStyle = '#FF4500'; // Orange red
    ctx.fillRect(x + w/2 + 8, y + h/2 + 6, 4, 2);

    // Side weapons/rockets
    ctx.fillStyle = '#8B0000'; // Dark red
    ctx.fillRect(x + 2, y + h/2 + 4, 8, 3); // Left weapon pod
    ctx.fillRect(x + w - 10, y + h/2 + 4, 8, 3); // Right weapon pod

    ctx.restore();
  }

  private renderHealthBar(ctx: CanvasRenderingContext2D) {
    const barWidth = this.width;
    const barHeight = 4;
    const barX = this.position.x;
    const barY = this.position.y - 8;

    // Background
    ctx.fillStyle = '#FF0000';
    ctx.fillRect(barX, barY, barWidth, barHeight);

    // Health
    const healthPercent = this.health / this.maxHealth;
    ctx.fillStyle = '#00FF00';
    ctx.fillRect(barX, barY, barWidth * healthPercent, barHeight);
  }

  getScoreValue(): number {
    switch (this.type) {
      case EnemyType.SOLDIER: return 100;
      case EnemyType.TANK: return 300;
      case EnemyType.HELICOPTER: return 200;
      default: return 100;
    }
  }

  getPosition(): Vector2 {
    return { ...this.position };
  }

  getBounds() {
    return {
      x: this.position.x,
      y: this.position.y,
      width: this.width,
      height: this.height
    };
  }
}
