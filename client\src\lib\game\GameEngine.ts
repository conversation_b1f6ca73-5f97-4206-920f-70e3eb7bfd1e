import { Player } from './Player';
import { Enemy } from './Enemy';
import { Bullet } from './Bullet';
import { PowerUp } from './PowerUp';
import { Background } from './Background';
import { InputHandler } from './InputHandler';
import { CollisionDetection } from './CollisionDetection';
import { useContraGame } from '../stores/useContraGame';
import { useAudio } from '../stores/useAudio';
import { EnemyType, PowerUpType, WeaponType } from './types';

export class GameEngine {
  private ctx: CanvasRenderingContext2D;
  private width: number;
  private height: number;
  private lastTime: number = 0;
  
  private player: Player;
  private enemies: Enemy[] = [];
  private bullets: Bullet[] = [];
  private powerUps: PowerUp[] = [];
  private background: Background;
  private inputHandler: InputHandler;
  
  // Game timing
  private lastEnemySpawn: number = 0;
  private enemySpawnRate: number = 1500; // ms between enemy spawns (much slower for easier gameplay)
  private lastPowerUpSpawn: number = 0;
  private powerUpSpawnRate: number = 10000; // ms between power-up spawns
  
  // Performance tracking
  private frameCount: number = 0;
  private lastFpsUpdate: number = 0;
  private fps: number = 0;

  constructor(ctx: CanvasRenderingContext2D, width: number, height: number) {
    this.ctx = ctx;
    this.width = width;
    this.height = height;
    
    // Create a canvas element for input handling
    const canvas = ctx.canvas;
    this.inputHandler = new InputHandler(canvas);
    
    this.player = new Player(this.inputHandler, width, height);
    this.background = new Background(ctx, width, height);
    
    console.log('GameEngine initialized');
  }

  start() {
    console.log('Game started');
    console.log('Player position:', this.player.getPosition());
    console.log('Canvas size:', this.width, 'x', this.height);
    this.lastTime = performance.now();
    this.lastEnemySpawn = this.lastTime;
    this.lastPowerUpSpawn = this.lastTime;

    // Start background music
    this.playBackgroundMusic();
  }

  private playBackgroundMusic() {
    const audioStore = useAudio.getState();
    if (audioStore.backgroundMusic && !audioStore.isMuted) {
      audioStore.backgroundMusic.currentTime = 0;
      audioStore.backgroundMusic.play().catch(console.log);
    }
  }

  update(): boolean {
    const currentTime = performance.now();
    const deltaTime = (currentTime - this.lastTime) / 1000; // Convert to seconds
    this.lastTime = currentTime;

    // Update FPS counter
    this.updateFPS(currentTime);

    // Update game objects
    this.background.update();
    this.player.update(deltaTime);
    
    // Handle player shooting
    if (this.inputHandler.isShooting()) {
      this.handlePlayerShooting();
    }

    // Update and manage game objects
    this.updateBullets(deltaTime);
    this.updateEnemies(deltaTime);
    this.updatePowerUps(deltaTime);
    
    // Spawn new objects
    this.spawnEnemies(currentTime);
    this.spawnPowerUps(currentTime);
    
    // Handle collisions
    this.handleCollisions();
    
    // Clean up inactive objects
    this.cleanupObjects();

    // Check if player is still alive
    return this.player.active;
  }

  private updateFPS(currentTime: number) {
    this.frameCount++;
    if (currentTime - this.lastFpsUpdate >= 1000) {
      this.fps = this.frameCount;
      this.frameCount = 0;
      this.lastFpsUpdate = currentTime;
    }
  }

  private handlePlayerShooting() {
    const bulletPositions = this.player.shoot();
    bulletPositions.forEach(pos => {
      const bullet = new Bullet(
        pos,
        { x: 1, y: 0 }, // Direction right
        true, // isPlayerBullet
        this.width,
        this.height,
        this.getPlayerBulletDamage()
      );
      this.bullets.push(bullet);
      console.log('Player shot bullet at:', pos, 'damage:', this.getPlayerBulletDamage());
    });
  }

  private getPlayerBulletDamage(): number {
    const stats = this.player.getStats();
    switch (stats.weaponType) {
      case WeaponType.LASER:
        return 50;
      case WeaponType.RAPID:
        return 15;
      default:
        return 25;
    }
  }

  private updateBullets(deltaTime: number) {
    this.bullets.forEach(bullet => bullet.update(deltaTime));
  }

  private updateEnemies(deltaTime: number) {
    this.enemies.forEach(enemy => {
      enemy.update(deltaTime);
      
      // Enemy shooting
      const bulletPos = enemy.shoot();
      if (bulletPos) {
        const bullet = new Bullet(
          bulletPos,
          { x: -1, y: 0 }, // Direction left
          false, // isPlayerBullet
          this.width,
          this.height,
          20 // enemy bullet damage
        );
        this.bullets.push(bullet);
      }
    });
  }

  private updatePowerUps(deltaTime: number) {
    this.powerUps.forEach(powerUp => powerUp.update(deltaTime));
  }

  private spawnEnemies(currentTime: number) {
    if (currentTime - this.lastEnemySpawn >= this.enemySpawnRate) {
      this.lastEnemySpawn = currentTime;
      
      // Random enemy type
      const enemyTypes = [EnemyType.SOLDIER, EnemyType.TANK, EnemyType.HELICOPTER];
      const randomType = enemyTypes[Math.floor(Math.random() * enemyTypes.length)];
      
      const enemy = new Enemy(
        randomType,
        {
          x: this.width + 50,
          y: Math.random() * (this.height - 100) + 50
        },
        this.width,
        this.height
      );
      
      this.enemies.push(enemy);
      
      // Increase difficulty over time
      this.enemySpawnRate = Math.max(800, this.enemySpawnRate - 10);
    }
  }

  private spawnPowerUps(currentTime: number) {
    if (currentTime - this.lastPowerUpSpawn >= this.powerUpSpawnRate) {
      this.lastPowerUpSpawn = currentTime;
      
      const powerUpTypes = [
        PowerUpType.WEAPON_UPGRADE,
        PowerUpType.HEALTH,
        PowerUpType.EXTRA_LIFE,
        PowerUpType.SCORE_BONUS
      ];
      const randomType = powerUpTypes[Math.floor(Math.random() * powerUpTypes.length)];
      
      const powerUp = new PowerUp(
        randomType,
        {
          x: this.width + 30,
          y: Math.random() * (this.height - 60) + 30
        },
        this.width
      );
      
      this.powerUps.push(powerUp);
    }
  }

  private handleCollisions() {
    const gameStore = useContraGame.getState();
    const audioStore = useAudio.getState();

    // Player bullets vs Enemies
    this.bullets.forEach(bullet => {
      if (!bullet.active || !bullet.isPlayerBullet) return;
      
      this.enemies.forEach(enemy => {
        if (!enemy.active) return;
        
        if (CollisionDetection.checkAABB(bullet, enemy)) {
          console.log('Bullet hit enemy!', bullet.damage, enemy.health);
          bullet.hit();
          audioStore.playHit();
          
          const destroyed = enemy.takeDamage(bullet.damage);
          console.log('Enemy destroyed?', destroyed, 'Score value:', enemy.getScoreValue());
          if (destroyed) {
            gameStore.addScore(enemy.getScoreValue());
            audioStore.playSuccess();
            console.log('Score added:', enemy.getScoreValue());
          }
        }
      });
    });

    // Enemy bullets vs Player
    this.bullets.forEach(bullet => {
      if (!bullet.active || bullet.isPlayerBullet) return;
      
      if (CollisionDetection.checkAABB(bullet, this.player)) {
        bullet.hit();
        this.player.takeDamage(bullet.damage);
        audioStore.playHit();
        
        if (!this.player.active) {
          gameStore.loseLife();
        }
      }
    });

    // Player vs Enemies (collision damage)
    this.enemies.forEach(enemy => {
      if (!enemy.active) return;
      
      if (CollisionDetection.checkAABB(this.player, enemy)) {
        this.player.takeDamage(25);
        const destroyed = enemy.takeDamage(100); // Destroy enemy on collision
        if (destroyed) {
          gameStore.addScore(enemy.getScoreValue());
          console.log('Enemy destroyed by player collision, score added:', enemy.getScoreValue());
        }
        audioStore.playHit();
        
        if (!this.player.active) {
          gameStore.loseLife();
        }
      }
    });

    // Player vs PowerUps
    this.powerUps.forEach(powerUp => {
      if (!powerUp.active) return;
      
      if (CollisionDetection.checkAABB(this.player, powerUp)) {
        this.handlePowerUpCollection(powerUp);
        powerUp.collect();
        audioStore.playSuccess();
      }
    });
  }

  private handlePowerUpCollection(powerUp: PowerUp) {
    const gameStore = useContraGame.getState();
    
    switch (powerUp.type) {
      case PowerUpType.WEAPON_UPGRADE:
        // Cycle through weapon types
        const currentStats = this.player.getStats();
        const weaponTypes = [WeaponType.NORMAL, WeaponType.SPREAD, WeaponType.RAPID, WeaponType.LASER];
        const currentIndex = weaponTypes.indexOf(currentStats.weaponType);
        const nextIndex = (currentIndex + 1) % weaponTypes.length;
        this.player.upgradeWeapon(weaponTypes[nextIndex]);
        break;
        
      case PowerUpType.HEALTH:
        this.player.heal(powerUp.value);
        break;
        
      case PowerUpType.EXTRA_LIFE:
        // Handle extra life in game store if needed
        gameStore.addScore(0); // Trigger any life-related logic
        break;
        
      case PowerUpType.SCORE_BONUS:
        gameStore.addScore(powerUp.value);
        break;
    }
  }

  private cleanupObjects() {
    this.bullets = this.bullets.filter(bullet => bullet.active);
    this.enemies = this.enemies.filter(enemy => enemy.active);
    this.powerUps = this.powerUps.filter(powerUp => powerUp.active);
  }

  render() {
    // Clear canvas first
    this.ctx.clearRect(0, 0, this.width, this.height);

    // Render enhanced background with sky, mountains, etc.
    this.renderEnhancedBackground();

    // Render scrolling background elements
    this.background.render();

    // Render game objects directly (simpler approach)
    console.log('Rendering player at:', this.player.getPosition());
    this.player.render(this.ctx);

    console.log('Rendering', this.enemies.length, 'enemies');
    this.enemies.forEach(enemy => enemy.render(this.ctx));
    this.bullets.forEach(bullet => bullet.render(this.ctx));
    this.powerUps.forEach(powerUp => powerUp.render(this.ctx));

    // Add atmospheric effects
    this.renderAtmosphericEffects();

    // Render debug info in development
    this.renderDebugInfo();
  }

  private renderEnhancedBackground() {
    // Create classic Contra-style jungle/war background
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.height);
    gradient.addColorStop(0, '#4169E1'); // Royal blue sky
    gradient.addColorStop(0.3, '#87CEEB'); // Sky blue
    gradient.addColorStop(0.6, '#228B22'); // Forest green (jungle)
    gradient.addColorStop(0.8, '#006400'); // Dark green
    gradient.addColorStop(1, '#2F4F4F'); // Dark slate gray (ground)

    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.width, this.height);

    // Add classic Contra elements
    this.renderJungleBackground();
    this.renderMetalPlatforms();
    this.renderExplosions();
  }

  private renderJungleBackground() {
    this.ctx.save();

    // Draw jungle trees and foliage
    this.ctx.fillStyle = '#228B22'; // Forest green

    // Background trees
    const treePositions = [
      { x: this.width * 0.1, y: this.height * 0.3, width: 40, height: this.height * 0.4 },
      { x: this.width * 0.25, y: this.height * 0.25, width: 60, height: this.height * 0.5 },
      { x: this.width * 0.4, y: this.height * 0.35, width: 35, height: this.height * 0.35 },
      { x: this.width * 0.6, y: this.height * 0.2, width: 50, height: this.height * 0.55 },
      { x: this.width * 0.8, y: this.height * 0.3, width: 45, height: this.height * 0.45 },
    ];

    treePositions.forEach(tree => {
      // Tree trunk
      this.ctx.fillStyle = '#8B4513'; // Saddle brown
      this.ctx.fillRect(tree.x, tree.y + tree.height * 0.7, tree.width * 0.3, tree.height * 0.3);

      // Tree foliage
      this.ctx.fillStyle = '#228B22'; // Forest green
      this.ctx.beginPath();
      this.ctx.arc(tree.x + tree.width * 0.15, tree.y + tree.height * 0.3, tree.width * 0.4, 0, Math.PI * 2);
      this.ctx.fill();
    });

    this.ctx.restore();
  }

  private renderMetalPlatforms() {
    this.ctx.save();

    // Classic Contra metal bridge/platform at bottom
    const platformY = this.height * 0.85;
    const platformHeight = this.height * 0.15;

    // Metal platform gradient
    const metalGradient = this.ctx.createLinearGradient(0, platformY, 0, platformY + platformHeight);
    metalGradient.addColorStop(0, '#C0C0C0'); // Silver
    metalGradient.addColorStop(0.3, '#A9A9A9'); // Dark gray
    metalGradient.addColorStop(0.7, '#696969'); // Dim gray
    metalGradient.addColorStop(1, '#2F2F2F'); // Dark gray

    this.ctx.fillStyle = metalGradient;
    this.ctx.fillRect(0, platformY, this.width, platformHeight);

    // Add metal rivets/bolts
    this.ctx.fillStyle = '#4F4F4F';
    for (let x = 20; x < this.width; x += 40) {
      this.ctx.beginPath();
      this.ctx.arc(x, platformY + 10, 3, 0, Math.PI * 2);
      this.ctx.fill();
      this.ctx.beginPath();
      this.ctx.arc(x, platformY + platformHeight - 10, 3, 0, Math.PI * 2);
      this.ctx.fill();
    }

    // Add metal grating lines
    this.ctx.strokeStyle = '#2F2F2F';
    this.ctx.lineWidth = 1;
    for (let x = 0; x < this.width; x += 10) {
      this.ctx.beginPath();
      this.ctx.moveTo(x, platformY);
      this.ctx.lineTo(x, platformY + platformHeight);
      this.ctx.stroke();
    }

    this.ctx.restore();
  }

  private renderExplosions() {
    this.ctx.save();

    // Smaller, more classic explosions
    const time = Date.now() / 300;
    const explosionPositions = [
      { x: this.width * 0.2, y: this.height * 0.15, size: 30 + Math.sin(time) * 10 },
      { x: this.width * 0.7, y: this.height * 0.1, size: 40 + Math.cos(time * 1.2) * 15 },
    ];

    explosionPositions.forEach(explosion => {
      this.drawExplosion(explosion.x, explosion.y, explosion.size);
    });

    this.ctx.restore();
  }

  private drawExplosion(x: number, y: number, size: number) {
    // Create explosion gradient
    const explosionGradient = this.ctx.createRadialGradient(x, y, 0, x, y, size);
    explosionGradient.addColorStop(0, '#FFFF00'); // Bright yellow center
    explosionGradient.addColorStop(0.2, '#FF8C00'); // Orange
    explosionGradient.addColorStop(0.5, '#FF4500'); // Red-orange
    explosionGradient.addColorStop(0.8, '#8B0000'); // Dark red
    explosionGradient.addColorStop(1, 'rgba(139, 0, 0, 0)'); // Transparent edge

    this.ctx.fillStyle = explosionGradient;
    this.ctx.beginPath();
    this.ctx.arc(x, y, size, 0, Math.PI * 2);
    this.ctx.fill();

    // Add explosion sparks
    this.ctx.fillStyle = '#FFFF00';
    for (let i = 0; i < 8; i++) {
      const angle = (i / 8) * Math.PI * 2;
      const sparkX = x + Math.cos(angle) * size * 0.8;
      const sparkY = y + Math.sin(angle) * size * 0.8;
      this.ctx.fillRect(sparkX - 2, sparkY - 2, 4, 4);
    }
  }

  private renderWarSmoke() {
    this.ctx.save();
    this.ctx.globalAlpha = 0.7;

    // Dark war smoke clouds
    const smokePositions = [
      { x: this.width * 0.1, y: this.height * 0.25, size: 120 },
      { x: this.width * 0.4, y: this.height * 0.2, size: 150 },
      { x: this.width * 0.7, y: this.height * 0.3, size: 100 },
      { x: this.width * 0.9, y: this.height * 0.25, size: 80 },
    ];

    smokePositions.forEach(smoke => {
      this.drawWarSmoke(smoke.x, smoke.y, smoke.size);
    });

    this.ctx.restore();
  }

  private drawWarSmoke(x: number, y: number, size: number) {
    // Create dark smoke gradient
    const smokeGradient = this.ctx.createRadialGradient(x, y, 0, x, y, size);
    smokeGradient.addColorStop(0, '#2F2F2F'); // Dark gray center
    smokeGradient.addColorStop(0.3, '#1C1C1C'); // Very dark gray
    smokeGradient.addColorStop(0.6, '#0F0F0F'); // Almost black
    smokeGradient.addColorStop(1, 'rgba(15, 15, 15, 0)'); // Transparent edge

    this.ctx.fillStyle = smokeGradient;
    this.ctx.beginPath();
    this.ctx.arc(x, y, size, 0, Math.PI * 2);
    this.ctx.fill();

    // Add smaller smoke puffs
    for (let i = 0; i < 3; i++) {
      const puffX = x + (Math.random() - 0.5) * size;
      const puffY = y + (Math.random() - 0.5) * size * 0.5;
      this.ctx.beginPath();
      this.ctx.arc(puffX, puffY, size * 0.3, 0, Math.PI * 2);
      this.ctx.fill();
    }
  }

  private renderDestroyedBuildings() {
    this.ctx.save();
    this.ctx.fillStyle = '#1A1A1A'; // Very dark for destroyed buildings

    // Destroyed building silhouettes
    const buildings = [
      { x: this.width * 0.05, width: 60, height: this.height * 0.3 },
      { x: this.width * 0.2, width: 80, height: this.height * 0.25 },
      { x: this.width * 0.35, width: 50, height: this.height * 0.35 },
      { x: this.width * 0.55, width: 70, height: this.height * 0.28 },
      { x: this.width * 0.75, width: 90, height: this.height * 0.32 },
      { x: this.width * 0.9, width: 40, height: this.height * 0.2 },
    ];

    buildings.forEach(building => {
      const buildingY = this.height * 0.5;

      // Main building structure (damaged)
      this.ctx.fillRect(building.x, buildingY, building.width, building.height);

      // Add damage/holes
      this.ctx.fillStyle = '#FF4500'; // Fire/explosion damage
      this.ctx.fillRect(building.x + 10, buildingY + 20, 15, 25);
      this.ctx.fillRect(building.x + building.width - 20, buildingY + 40, 12, 20);

      // Reset color for next building
      this.ctx.fillStyle = '#1A1A1A';
    });

    this.ctx.restore();
  }

  private drawCloud(x: number, y: number, size: number) {
    this.ctx.beginPath();
    this.ctx.arc(x, y, size * 0.5, 0, Math.PI * 2);
    this.ctx.arc(x + size * 0.3, y, size * 0.4, 0, Math.PI * 2);
    this.ctx.arc(x - size * 0.3, y, size * 0.4, 0, Math.PI * 2);
    this.ctx.arc(x + size * 0.1, y - size * 0.3, size * 0.3, 0, Math.PI * 2);
    this.ctx.arc(x - size * 0.1, y - size * 0.3, size * 0.3, 0, Math.PI * 2);
    this.ctx.fill();
  }

  private renderMountains() {
    this.ctx.save();
    this.ctx.fillStyle = '#4682B4'; // Steel blue for distant mountains
    this.ctx.globalAlpha = 0.7;

    // Draw mountain silhouette
    this.ctx.beginPath();
    this.ctx.moveTo(0, this.height * 0.4);

    const peaks = [
      { x: this.width * 0.1, y: this.height * 0.25 },
      { x: this.width * 0.3, y: this.height * 0.3 },
      { x: this.width * 0.5, y: this.height * 0.2 },
      { x: this.width * 0.7, y: this.height * 0.35 },
      { x: this.width * 0.9, y: this.height * 0.28 },
    ];

    peaks.forEach(peak => {
      this.ctx.lineTo(peak.x, peak.y);
    });

    this.ctx.lineTo(this.width, this.height * 0.4);
    this.ctx.lineTo(this.width, this.height * 0.5);
    this.ctx.lineTo(0, this.height * 0.5);
    this.ctx.closePath();
    this.ctx.fill();

    this.ctx.restore();
  }

  private renderAtmosphericEffects() {
    // Add subtle screen effects
    this.ctx.save();

    // Vignette effect
    const vignette = this.ctx.createRadialGradient(
      this.width / 2, this.height / 2, 0,
      this.width / 2, this.height / 2, Math.max(this.width, this.height) / 2
    );
    vignette.addColorStop(0, 'rgba(0, 0, 0, 0)');
    vignette.addColorStop(1, 'rgba(0, 0, 0, 0.3)');

    this.ctx.fillStyle = vignette;
    this.ctx.fillRect(0, 0, this.width, this.height);

    // Add subtle noise/grain
    this.ctx.globalAlpha = 0.05;
    for (let i = 0; i < 100; i++) {
      this.ctx.fillStyle = Math.random() > 0.5 ? '#FFFFFF' : '#000000';
      this.ctx.fillRect(
        Math.random() * this.width,
        Math.random() * this.height,
        1, 1
      );
    }

    this.ctx.restore();
  }

  private renderDebugInfo() {
    // Always show debug info for now to help with troubleshooting
    this.ctx.save();
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.font = '14px monospace';
    this.ctx.textAlign = 'left';
    this.ctx.shadowColor = '#000000';
    this.ctx.shadowBlur = 2;

    const debugInfo = [
      `FPS: ${this.fps}`,
      `Enemies: ${this.enemies.length}`,
      `Bullets: ${this.bullets.length}`,
      `PowerUps: ${this.powerUps.length}`,
      `Player Health: ${this.player.getStats().health}`,
      `Player Pos: ${Math.round(this.player.getPosition().x)}, ${Math.round(this.player.getPosition().y)}`
    ];

    debugInfo.forEach((info, index) => {
      this.ctx.fillText(info, 10, 30 + (index * 20));
    });

    this.ctx.restore();
  }

  resize(width: number, height: number) {
    this.width = width;
    this.height = height;
    this.player.resize(width, height);
    this.background.resize(width, height);
  }

  cleanup() {
    this.inputHandler.cleanup();
    
    // Stop background music
    const audioStore = useAudio.getState();
    if (audioStore.backgroundMusic) {
      audioStore.backgroundMusic.pause();
      audioStore.backgroundMusic.currentTime = 0;
    }
  }
}
