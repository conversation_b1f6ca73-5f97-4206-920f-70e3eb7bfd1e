import * as THREE from 'three';

export enum Enemy3DType {
  SOLDIER = 'soldier',
  TANK = 'tank',
  HELICOPTER = 'helicopter'
}

export class Enemy3D {
  private scene: THREE.Scene;
  private mesh: THREE.Group;
  private position: THREE.Vector3;
  private velocity: THREE.Vector3;
  private health: number;
  private maxHealth: number;
  private active: boolean = true;
  private type: Enemy3DType;
  
  // Movement
  private speed: number;
  private movePattern: 'straight' | 'zigzag' | 'circle' = 'straight';
  private patternTime: number = 0;
  
  // Combat
  private lastShotTime: number = 0;
  private shotCooldown: number = 1000;
  private bullets: THREE.Mesh[] = [];
  
  // Animation
  private mixer: THREE.AnimationMixer | null = null;
  private rotationSpeed: number = 0;
  
  constructor(scene: THREE.Scene, type: Enemy3DType = Enemy3DType.SOLDIER) {
    this.scene = scene;
    this.type = type;
    this.position = new THREE.Vector3(0, 0, 0);
    this.velocity = new THREE.Vector3(0, 0, 1); // Move toward player
    
    this.setupEnemyStats();
    this.createEnemyModel();
  }
  
  private setupEnemyStats() {
    switch (this.type) {
      case Enemy3DType.SOLDIER:
        this.health = 50;
        this.maxHealth = 50;
        this.speed = 8;
        this.shotCooldown = 1500;
        this.movePattern = Math.random() > 0.5 ? 'straight' : 'zigzag';
        break;
        
      case Enemy3DType.TANK:
        this.health = 200;
        this.maxHealth = 200;
        this.speed = 3;
        this.shotCooldown = 2000;
        this.movePattern = 'straight';
        break;
        
      case Enemy3DType.HELICOPTER:
        this.health = 100;
        this.maxHealth = 100;
        this.speed = 12;
        this.shotCooldown = 800;
        this.movePattern = 'circle';
        this.rotationSpeed = 0.1;
        break;
    }
  }
  
  private createEnemyModel() {
    this.mesh = new THREE.Group();
    
    switch (this.type) {
      case Enemy3DType.SOLDIER:
        this.createSoldierModel();
        break;
      case Enemy3DType.TANK:
        this.createTankModel();
        break;
      case Enemy3DType.HELICOPTER:
        this.createHelicopterModel();
        break;
    }
    
    this.mesh.position.copy(this.position);
    this.scene.add(this.mesh);
  }
  
  private createSoldierModel() {
    // Body
    const bodyGeometry = new THREE.BoxGeometry(1, 1.8, 0.6);
    const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 }); // Dark red uniform
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.position.y = 0.9;
    body.castShadow = true;
    this.mesh.add(body);
    
    // Head
    const headGeometry = new THREE.SphereGeometry(0.3, 12, 12);
    const headMaterial = new THREE.MeshLambertMaterial({ color: 0xFDBCB4 });
    const head = new THREE.Mesh(headGeometry, headMaterial);
    head.position.y = 2.1;
    head.castShadow = true;
    this.mesh.add(head);
    
    // Helmet
    const helmetGeometry = new THREE.SphereGeometry(0.35, 12, 12, 0, Math.PI * 2, 0, Math.PI / 2);
    const helmetMaterial = new THREE.MeshLambertMaterial({ color: 0x2F2F2F });
    const helmet = new THREE.Mesh(helmetGeometry, helmetMaterial);
    helmet.position.y = 2.1;
    helmet.castShadow = true;
    this.mesh.add(helmet);
    
    // Weapon
    const weaponGeometry = new THREE.BoxGeometry(0.08, 0.08, 1.5);
    const weaponMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
    const weapon = new THREE.Mesh(weaponGeometry, weaponMaterial);
    weapon.position.set(0.4, 1.2, -0.3);
    weapon.rotation.y = -Math.PI / 4;
    weapon.castShadow = true;
    this.mesh.add(weapon);
    
    // Arms
    const armGeometry = new THREE.BoxGeometry(0.3, 1.2, 0.3);
    const armMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 });
    
    const leftArm = new THREE.Mesh(armGeometry, armMaterial);
    leftArm.position.set(-0.65, 0.9, 0);
    leftArm.castShadow = true;
    this.mesh.add(leftArm);
    
    const rightArm = new THREE.Mesh(armGeometry, armMaterial);
    rightArm.position.set(0.65, 0.9, 0);
    rightArm.castShadow = true;
    this.mesh.add(rightArm);
    
    // Legs
    const legGeometry = new THREE.BoxGeometry(0.3, 1.2, 0.3);
    const legMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 });
    
    const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
    leftLeg.position.set(-0.25, -0.6, 0);
    leftLeg.castShadow = true;
    this.mesh.add(leftLeg);
    
    const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
    rightLeg.position.set(0.25, -0.6, 0);
    rightLeg.castShadow = true;
    this.mesh.add(rightLeg);
  }
  
  private createTankModel() {
    // Tank body
    const bodyGeometry = new THREE.BoxGeometry(3, 1, 4);
    const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4A4A4A });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.position.y = 0.5;
    body.castShadow = true;
    this.mesh.add(body);
    
    // Tank turret
    const turretGeometry = new THREE.CylinderGeometry(0.8, 0.8, 0.6, 16);
    const turretMaterial = new THREE.MeshLambertMaterial({ color: 0x3A3A3A });
    const turret = new THREE.Mesh(turretGeometry, turretMaterial);
    turret.position.y = 1.3;
    turret.castShadow = true;
    this.mesh.add(turret);
    
    // Tank cannon
    const cannonGeometry = new THREE.CylinderGeometry(0.08, 0.08, 3, 8);
    const cannonMaterial = new THREE.MeshLambertMaterial({ color: 0x2A2A2A });
    const cannon = new THREE.Mesh(cannonGeometry, cannonMaterial);
    cannon.position.set(0, 1.3, -2.5);
    cannon.rotation.x = Math.PI / 2;
    cannon.castShadow = true;
    this.mesh.add(cannon);
    
    // Tank tracks (simplified)
    const trackGeometry = new THREE.BoxGeometry(3.2, 0.4, 4.2);
    const trackMaterial = new THREE.MeshLambertMaterial({ color: 0x1A1A1A });
    const tracks = new THREE.Mesh(trackGeometry, trackMaterial);
    tracks.position.y = -0.2;
    tracks.castShadow = true;
    this.mesh.add(tracks);
  }
  
  private createHelicopterModel() {
    // Helicopter body
    const bodyGeometry = new THREE.CapsuleGeometry(0.5, 2, 8, 16);
    const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x2F4F2F });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.rotation.z = Math.PI / 2;
    body.position.y = 2;
    body.castShadow = true;
    this.mesh.add(body);
    
    // Main rotor
    const rotorGeometry = new THREE.BoxGeometry(6, 0.05, 0.2);
    const rotorMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
    const rotor = new THREE.Mesh(rotorGeometry, rotorMaterial);
    rotor.position.y = 3;
    this.mesh.add(rotor);
    
    // Tail rotor
    const tailRotorGeometry = new THREE.BoxGeometry(0.8, 0.05, 0.1);
    const tailRotor = new THREE.Mesh(tailRotorGeometry, rotorMaterial);
    tailRotor.position.set(-1.5, 2.5, 0);
    tailRotor.rotation.y = Math.PI / 2;
    this.mesh.add(tailRotor);
    
    // Landing skids
    const skidGeometry = new THREE.BoxGeometry(2, 0.1, 0.2);
    const skidMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
    
    const leftSkid = new THREE.Mesh(skidGeometry, skidMaterial);
    leftSkid.position.set(0, 1.2, -0.6);
    this.mesh.add(leftSkid);
    
    const rightSkid = new THREE.Mesh(skidGeometry, skidMaterial);
    rightSkid.position.set(0, 1.2, 0.6);
    this.mesh.add(rightSkid);
  }
  
  setPosition(x: number, y: number, z: number) {
    this.position.set(x, y, z);
    this.mesh.position.copy(this.position);
  }
  
  update(deltaTime: number) {
    if (!this.active) return;
    
    this.updateMovement(deltaTime);
    this.updateAnimation(deltaTime);
    this.updateBullets(deltaTime);
    
    // Update mesh position
    this.mesh.position.copy(this.position);
    
    // Remove if too far behind player
    if (this.position.z > 30) {
      this.destroy();
    }
  }
  
  private updateMovement(deltaTime: number) {
    this.patternTime += deltaTime;
    
    switch (this.movePattern) {
      case 'straight':
        this.velocity.set(0, 0, this.speed);
        break;
        
      case 'zigzag':
        this.velocity.set(
          Math.sin(this.patternTime * 2) * this.speed * 0.5,
          0,
          this.speed
        );
        break;
        
      case 'circle':
        const radius = 5;
        this.velocity.set(
          Math.cos(this.patternTime) * radius,
          Math.sin(this.patternTime * 0.5) * 2,
          this.speed * 0.3
        );
        break;
    }
    
    this.position.add(this.velocity.clone().multiplyScalar(deltaTime));
    
    // Face movement direction
    if (this.velocity.length() > 0) {
      this.mesh.lookAt(this.position.clone().add(this.velocity));
    }
  }
  
  private updateAnimation(deltaTime: number) {
    if (this.type === Enemy3DType.HELICOPTER) {
      // Rotate rotors
      const rotors = this.mesh.children.filter(child => 
        child.geometry && (child.geometry as any).parameters?.width > 2
      );
      rotors.forEach(rotor => {
        rotor.rotation.y += this.rotationSpeed * deltaTime * 100;
      });
    }
    
    if (this.mixer) {
      this.mixer.update(deltaTime);
    }
  }
  
  private updateBullets(deltaTime: number) {
    // Update existing bullets
    this.bullets.forEach(bullet => {
      bullet.position.z += 20 * deltaTime; // Move toward player
    });
    
    // Remove bullets that are too far
    this.bullets = this.bullets.filter(bullet => {
      if (bullet.position.z > 20) {
        this.scene.remove(bullet);
        return false;
      }
      return true;
    });
  }
  
  takeDamage(damage: number): boolean {
    this.health -= damage;
    
    // Flash red when hit
    this.mesh.children.forEach(child => {
      if (child instanceof THREE.Mesh) {
        const material = child.material as THREE.MeshLambertMaterial;
        const originalColor = material.color.clone();
        material.color.setHex(0xff0000);
        setTimeout(() => {
          material.color.copy(originalColor);
        }, 100);
      }
    });
    
    if (this.health <= 0) {
      this.destroy();
      return true;
    }
    return false;
  }
  
  destroy() {
    this.active = false;
    this.scene.remove(this.mesh);
    
    // Clean up bullets
    this.bullets.forEach(bullet => this.scene.remove(bullet));
    this.bullets = [];
    
    // Dispose of geometries and materials
    this.mesh.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        child.geometry.dispose();
        if (Array.isArray(child.material)) {
          child.material.forEach(material => material.dispose());
        } else {
          child.material.dispose();
        }
      }
    });
  }
  
  isActive(): boolean {
    return this.active;
  }
  
  getPosition(): THREE.Vector3 {
    return this.position.clone();
  }
  
  getHealth(): number {
    return this.health;
  }
  
  getMaxHealth(): number {
    return this.maxHealth;
  }
}
