import * as THREE from 'three';

interface Particle {
  position: THREE.Vector3;
  velocity: THREE.Vector3;
  life: number;
  maxLife: number;
  size: number;
  color: THREE.Color;
  mesh: THREE.Mesh;
}

export class ParticleSystem {
  private scene: THREE.Scene;
  private particles: Particle[] = [];
  private particlePool: THREE.Mesh[] = [];
  
  // Particle materials
  private explosionMaterial: THREE.MeshBasicMaterial;
  private smokeMaterial: THREE.MeshBasicMaterial;
  private sparkMaterial: THREE.MeshBasicMaterial;
  private dustMaterial: THREE.MeshBasicMaterial;
  
  constructor(scene: THREE.Scene) {
    this.scene = scene;
    this.initializeMaterials();
    this.createParticlePool();
    this.createAmbientEffects();
  }
  
  private initializeMaterials() {
    // Explosion particles (fire/smoke)
    this.explosionMaterial = new THREE.MeshBasicMaterial({
      color: 0xff4400,
      transparent: true,
      opacity: 0.8,
      blending: THREE.AdditiveBlending
    });
    
    // Smoke particles
    this.smokeMaterial = new THREE.MeshBasicMaterial({
      color: 0x666666,
      transparent: true,
      opacity: 0.6,
      blending: THREE.NormalBlending
    });
    
    // Spark particles
    this.sparkMaterial = new THREE.MeshBasicMaterial({
      color: 0xffff00,
      transparent: true,
      opacity: 1.0,
      blending: THREE.AdditiveBlending
    });
    
    // Dust particles
    this.dustMaterial = new THREE.MeshBasicMaterial({
      color: 0x8B7355,
      transparent: true,
      opacity: 0.4,
      blending: THREE.NormalBlending
    });
  }
  
  private createParticlePool() {
    // Pre-create particle meshes for performance
    const particleGeometry = new THREE.SphereGeometry(0.1, 6, 6);
    
    for (let i = 0; i < 200; i++) {
      const mesh = new THREE.Mesh(particleGeometry, this.explosionMaterial.clone());
      mesh.visible = false;
      this.particlePool.push(mesh);
      this.scene.add(mesh);
    }
  }
  
  private getParticleFromPool(): THREE.Mesh | null {
    for (const mesh of this.particlePool) {
      if (!mesh.visible) {
        mesh.visible = true;
        return mesh;
      }
    }
    return null; // Pool exhausted
  }
  
  private returnParticleToPool(mesh: THREE.Mesh) {
    mesh.visible = false;
    mesh.position.set(0, 0, 0);
    mesh.scale.set(1, 1, 1);
  }
  
  createExplosion(position: THREE.Vector3, intensity: number = 1) {
    const particleCount = Math.floor(20 * intensity);
    
    for (let i = 0; i < particleCount; i++) {
      const mesh = this.getParticleFromPool();
      if (!mesh) break;
      
      // Set material based on particle type
      const rand = Math.random();
      if (rand < 0.4) {
        mesh.material = this.explosionMaterial.clone();
        (mesh.material as THREE.MeshBasicMaterial).color.setHSL(
          0.1 + Math.random() * 0.1, // Orange to red
          1,
          0.5 + Math.random() * 0.3
        );
      } else if (rand < 0.7) {
        mesh.material = this.smokeMaterial.clone();
      } else {
        mesh.material = this.sparkMaterial.clone();
      }
      
      const particle: Particle = {
        position: position.clone(),
        velocity: new THREE.Vector3(
          (Math.random() - 0.5) * 20 * intensity,
          Math.random() * 15 * intensity,
          (Math.random() - 0.5) * 20 * intensity
        ),
        life: 1.0,
        maxLife: 1.0 + Math.random() * 2,
        size: 0.2 + Math.random() * 0.3 * intensity,
        color: new THREE.Color().setHSL(
          0.1 + Math.random() * 0.1,
          1,
          0.5 + Math.random() * 0.3
        ),
        mesh
      };
      
      mesh.position.copy(position);
      mesh.scale.setScalar(particle.size);
      
      this.particles.push(particle);
    }
  }
  
  createHitEffect(position: THREE.Vector3) {
    const particleCount = 8;
    
    for (let i = 0; i < particleCount; i++) {
      const mesh = this.getParticleFromPool();
      if (!mesh) break;
      
      mesh.material = this.sparkMaterial.clone();
      
      const particle: Particle = {
        position: position.clone(),
        velocity: new THREE.Vector3(
          (Math.random() - 0.5) * 10,
          Math.random() * 8,
          (Math.random() - 0.5) * 10
        ),
        life: 0.5,
        maxLife: 0.5,
        size: 0.05 + Math.random() * 0.1,
        color: new THREE.Color(0xffff00),
        mesh
      };
      
      mesh.position.copy(position);
      mesh.scale.setScalar(particle.size);
      
      this.particles.push(particle);
    }
  }
  
  createMuzzleFlash(position: THREE.Vector3, direction: THREE.Vector3) {
    const particleCount = 5;
    
    for (let i = 0; i < particleCount; i++) {
      const mesh = this.getParticleFromPool();
      if (!mesh) break;
      
      mesh.material = this.explosionMaterial.clone();
      (mesh.material as THREE.MeshBasicMaterial).color.setHex(0xffaa00);
      
      const spread = 0.3;
      const particle: Particle = {
        position: position.clone(),
        velocity: direction.clone()
          .multiplyScalar(5)
          .add(new THREE.Vector3(
            (Math.random() - 0.5) * spread,
            (Math.random() - 0.5) * spread,
            (Math.random() - 0.5) * spread
          )),
        life: 0.1,
        maxLife: 0.1,
        size: 0.1 + Math.random() * 0.1,
        color: new THREE.Color(0xffaa00),
        mesh
      };
      
      mesh.position.copy(position);
      mesh.scale.setScalar(particle.size);
      
      this.particles.push(particle);
    }
  }
  
  createDustCloud(position: THREE.Vector3) {
    const particleCount = 15;
    
    for (let i = 0; i < particleCount; i++) {
      const mesh = this.getParticleFromPool();
      if (!mesh) break;
      
      mesh.material = this.dustMaterial.clone();
      
      const particle: Particle = {
        position: position.clone(),
        velocity: new THREE.Vector3(
          (Math.random() - 0.5) * 5,
          Math.random() * 3,
          (Math.random() - 0.5) * 5
        ),
        life: 2.0,
        maxLife: 2.0 + Math.random(),
        size: 0.3 + Math.random() * 0.5,
        color: new THREE.Color(0x8B7355),
        mesh
      };
      
      mesh.position.copy(position);
      mesh.scale.setScalar(particle.size);
      
      this.particles.push(particle);
    }
  }
  
  private createAmbientEffects() {
    // Create floating dust particles for atmosphere
    setInterval(() => {
      if (Math.random() < 0.3) {
        const position = new THREE.Vector3(
          (Math.random() - 0.5) * 100,
          Math.random() * 20 + 5,
          -50 - Math.random() * 50
        );
        this.createDustCloud(position);
      }
    }, 2000);
  }
  
  update(deltaTime: number) {
    // Update all particles
    for (let i = this.particles.length - 1; i >= 0; i--) {
      const particle = this.particles[i];
      
      // Update position
      particle.position.add(particle.velocity.clone().multiplyScalar(deltaTime));
      particle.mesh.position.copy(particle.position);
      
      // Apply gravity to some particles
      if (particle.velocity.y > -20) {
        particle.velocity.y -= 9.8 * deltaTime; // Gravity
      }
      
      // Apply air resistance
      particle.velocity.multiplyScalar(0.98);
      
      // Update life
      particle.life -= deltaTime;
      
      // Update visual properties based on life
      const lifeRatio = particle.life / particle.maxLife;
      const material = particle.mesh.material as THREE.MeshBasicMaterial;
      
      // Fade out over time
      material.opacity = lifeRatio * 0.8;
      
      // Change color for fire particles
      if (material === this.explosionMaterial || 
          (material as any).color?.getHex() === 0xff4400) {
        const hue = 0.1 - (1 - lifeRatio) * 0.05; // Orange to red
        material.color.setHSL(hue, 1, 0.5 * lifeRatio);
      }
      
      // Scale particles
      const scale = particle.size * (0.5 + lifeRatio * 0.5);
      particle.mesh.scale.setScalar(scale);
      
      // Remove dead particles
      if (particle.life <= 0 || particle.position.y < -5) {
        this.returnParticleToPool(particle.mesh);
        this.particles.splice(i, 1);
      }
    }
  }
  
  // Advanced particle effects
  createBloodSplatter(position: THREE.Vector3) {
    const particleCount = 12;

    for (let i = 0; i < particleCount; i++) {
      const mesh = this.getParticleFromPool();
      if (!mesh) break;

      const bloodMaterial = new THREE.MeshBasicMaterial({
        color: 0x8B0000,
        transparent: true,
        opacity: 0.8
      });
      mesh.material = bloodMaterial;

      const particle: Particle = {
        position: position.clone(),
        velocity: new THREE.Vector3(
          (Math.random() - 0.5) * 8,
          Math.random() * 5,
          (Math.random() - 0.5) * 8
        ),
        life: 3.0,
        maxLife: 3.0,
        size: 0.1 + Math.random() * 0.2,
        color: new THREE.Color(0x8B0000),
        mesh
      };

      mesh.position.copy(position);
      mesh.scale.setScalar(particle.size);

      this.particles.push(particle);
    }
  }

  createShellCasing(position: THREE.Vector3, direction: THREE.Vector3) {
    const mesh = this.getParticleFromPool();
    if (!mesh) return;

    // Create shell casing geometry
    const casingGeometry = new THREE.CylinderGeometry(0.02, 0.025, 0.1, 8);
    const casingMaterial = new THREE.MeshLambertMaterial({ color: 0xFFD700 });

    mesh.geometry.dispose();
    mesh.geometry = casingGeometry;
    mesh.material = casingMaterial;

    const ejectDirection = new THREE.Vector3(
      direction.x + (Math.random() - 0.5) * 0.5,
      0.5 + Math.random() * 0.3,
      direction.z + (Math.random() - 0.5) * 0.5
    );

    const particle: Particle = {
      position: position.clone(),
      velocity: ejectDirection.multiplyScalar(3),
      life: 5.0,
      maxLife: 5.0,
      size: 1,
      color: new THREE.Color(0xFFD700),
      mesh
    };

    mesh.position.copy(position);
    this.particles.push(particle);
  }

  createRicochetSparks(position: THREE.Vector3, normal: THREE.Vector3) {
    const particleCount = 6;

    for (let i = 0; i < particleCount; i++) {
      const mesh = this.getParticleFromPool();
      if (!mesh) break;

      mesh.material = this.sparkMaterial.clone();

      // Reflect direction based on surface normal
      const reflectDirection = normal.clone()
        .add(new THREE.Vector3(
          (Math.random() - 0.5) * 0.5,
          (Math.random() - 0.5) * 0.5,
          (Math.random() - 0.5) * 0.5
        ))
        .normalize();

      const particle: Particle = {
        position: position.clone(),
        velocity: reflectDirection.multiplyScalar(8 + Math.random() * 4),
        life: 0.3,
        maxLife: 0.3,
        size: 0.03 + Math.random() * 0.05,
        color: new THREE.Color(0xffaa00),
        mesh
      };

      mesh.position.copy(position);
      mesh.scale.setScalar(particle.size);

      this.particles.push(particle);
    }
  }

  createDebrisField(position: THREE.Vector3, intensity: number = 1) {
    const particleCount = Math.floor(15 * intensity);

    for (let i = 0; i < particleCount; i++) {
      const mesh = this.getParticleFromPool();
      if (!mesh) break;

      // Create random debris geometry
      const debrisTypes = ['box', 'sphere', 'cylinder'];
      const type = debrisTypes[Math.floor(Math.random() * debrisTypes.length)];

      let geometry: THREE.BufferGeometry;
      switch (type) {
        case 'box':
          geometry = new THREE.BoxGeometry(
            0.1 + Math.random() * 0.2,
            0.1 + Math.random() * 0.2,
            0.1 + Math.random() * 0.2
          );
          break;
        case 'sphere':
          geometry = new THREE.SphereGeometry(0.05 + Math.random() * 0.1, 6, 6);
          break;
        default:
          geometry = new THREE.CylinderGeometry(0.03, 0.03, 0.1 + Math.random() * 0.1, 6);
          break;
      }

      const debrisMaterial = new THREE.MeshLambertMaterial({
        color: new THREE.Color().setHSL(0, 0, 0.2 + Math.random() * 0.3)
      });

      mesh.geometry.dispose();
      mesh.geometry = geometry;
      mesh.material = debrisMaterial;

      const particle: Particle = {
        position: position.clone(),
        velocity: new THREE.Vector3(
          (Math.random() - 0.5) * 15 * intensity,
          Math.random() * 10 * intensity,
          (Math.random() - 0.5) * 15 * intensity
        ),
        life: 4.0,
        maxLife: 4.0,
        size: 1,
        color: new THREE.Color(0x666666),
        mesh
      };

      mesh.position.copy(position);
      mesh.rotation.set(
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2
      );

      this.particles.push(particle);
    }
  }

  createSmokeTrail(startPosition: THREE.Vector3, endPosition: THREE.Vector3) {
    const distance = startPosition.distanceTo(endPosition);
    const particleCount = Math.floor(distance * 2);

    for (let i = 0; i < particleCount; i++) {
      const mesh = this.getParticleFromPool();
      if (!mesh) break;

      mesh.material = this.smokeMaterial.clone();

      const t = i / particleCount;
      const position = startPosition.clone().lerp(endPosition, t);

      const particle: Particle = {
        position,
        velocity: new THREE.Vector3(
          (Math.random() - 0.5) * 2,
          Math.random() * 3,
          (Math.random() - 0.5) * 2
        ),
        life: 2.0 + Math.random(),
        maxLife: 2.0 + Math.random(),
        size: 0.2 + Math.random() * 0.3,
        color: new THREE.Color(0x666666),
        mesh
      };

      mesh.position.copy(position);
      mesh.scale.setScalar(particle.size);

      this.particles.push(particle);
    }
  }

  // Clean up all particles
  cleanup() {
    this.particles.forEach(particle => {
      this.returnParticleToPool(particle.mesh);
    });
    this.particles = [];

    // Dispose of materials
    this.explosionMaterial.dispose();
    this.smokeMaterial.dispose();
    this.sparkMaterial.dispose();
    this.dustMaterial.dispose();
  }
}
