# Overview

This is a full-stack web application featuring a Contra-style side-scrolling shooter game built with React and TypeScript. The application uses a modern tech stack with Express.js backend, React frontend, PostgreSQL database with Drizzle ORM, and comprehensive UI components from Radix UI with Tailwind CSS styling.

# User Preferences

Preferred communication style: Simple, everyday language.

# System Architecture

## Frontend Architecture
- **Framework**: React 18 with TypeScript and Vite for development/building
- **Styling**: Tailwind CSS with custom theme configuration and CSS variables
- **UI Components**: Comprehensive Radix UI component library with custom shadcn/ui implementations
- **3D Graphics**: React Three Fiber (@react-three/fiber) with additional tools from @react-three/drei and post-processing effects
- **State Management**: Zustand stores for game state, audio management, and general application state
- **Game Engine**: Custom 2D canvas-based game engine with classes for Player, Enemy, Bullet, PowerUp, Background, and collision detection
- **Query Management**: TanStack React Query for server state management

## Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ES modules
- **Development**: Hot reloading with Vite integration for SSR development
- **Storage**: In-memory storage implementation with interface for database operations
- **API Structure**: RESTful API with routes prefixed under `/api`

## Data Storage
- **Database**: PostgreSQL configured through Drizzle ORM
- **Connection**: Neon Database serverless connection (@neondatabase/serverless)
- **Schema Management**: Drizzle Kit for migrations and schema management
- **Tables**: Users table with basic authentication fields (username, password)
- **Session Storage**: PostgreSQL session store (connect-pg-simple) configured

## Game Architecture
- **Engine**: Custom 2D canvas-based game engine
- **Input Handling**: Keyboard event system with game-specific key mappings
- **Physics**: Basic collision detection with AABB and circle collision methods
- **Audio System**: HTML5 Audio API with mute/unmute functionality
- **Game Objects**: Object-oriented design with Player, Enemy, Bullet, PowerUp classes
- **Background**: Parallax scrolling background with multiple layers

# Key Components

## Game System
- **GameEngine**: Main game loop with entity management and collision detection
- **Player**: Character controller with input handling and weapon systems
- **Enemy**: AI-controlled entities with different types (soldier, tank, helicopter)
- **Bullet**: Projectile system with player/enemy differentiation
- **PowerUp**: Collectible items for weapon upgrades, health, and score bonuses

## State Management
- **useContraGame**: Game-specific state (score, lives, game state transitions)
- **useAudio**: Audio management with mute controls and sound effects
- **useGame**: General game phase management

## UI System
- **GameCanvas**: 2D canvas rendering component
- **GameMenu**: Main menu interface
- **GameUI**: In-game HUD and game over screens
- **SoundManager**: Audio initialization and management

# Data Flow

1. **Game Initialization**: Canvas setup → Game engine creation → Asset loading
2. **Game Loop**: Input handling → Game state updates → Collision detection → Rendering
3. **State Updates**: Game events → Zustand store updates → UI re-rendering
4. **Audio Management**: Sound events → Audio store → HTML5 Audio playback
5. **Server Communication**: Future API calls through TanStack Query → Express routes → Database operations

# External Dependencies

## Frontend Libraries
- **Radix UI**: Complete UI component library for accessible interfaces
- **React Three Fiber**: 3D rendering capabilities (prepared for future 3D features)
- **TanStack React Query**: Server state management and caching
- **Zustand**: Client-side state management
- **Tailwind CSS**: Utility-first CSS framework

## Backend Libraries
- **Drizzle ORM**: Type-safe database operations
- **Neon Database**: Serverless PostgreSQL hosting
- **Express.js**: Web application framework

## Development Tools
- **Vite**: Fast development server and build tool
- **TypeScript**: Type safety and development experience
- **ESBuild**: Fast JavaScript bundler for production

# Deployment Strategy

## Build Process
- **Frontend**: Vite builds React app to `dist/public`
- **Backend**: ESBuild bundles server code to `dist/index.js`
- **Database**: Drizzle migrations applied via `db:push` command

## Environment Setup
- **Development**: `npm run dev` - TSX runs server with Vite middleware
- **Production**: `npm run build` then `npm start` - Node.js serves built application
- **Database**: PostgreSQL connection via `DATABASE_URL` environment variable

## File Structure
- **Client**: All frontend code in `client/` directory
- **Server**: Backend code in `server/` directory  
- **Shared**: Common types and schemas in `shared/` directory
- **Build Output**: Compiled code in `dist/` directory

The application is designed as a monorepo with clear separation between client and server code, using modern TypeScript tooling throughout the stack.