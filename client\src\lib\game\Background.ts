export class Background {
  private ctx: CanvasRenderingContext2D;
  private width: number;
  private height: number;
  private scrollSpeed: number = 2;
  private layers: BackgroundLayer[] = [];

  constructor(ctx: CanvasRenderingContext2D, width: number, height: number) {
    this.ctx = ctx;
    this.width = width;
    this.height = height;
    this.initializeLayers();
  }

  private initializeLayers() {
    // Background sky (war-torn)
    this.layers.push({
      color: '#2F2F2F',
      scrollSpeed: 0.1,
      elements: this.generateClouds()
    });

    // Distant mountains/buildings
    this.layers.push({
      color: '#404040',
      scrollSpeed: 0.3,
      elements: this.generateCityscape()
    });

    // Mid-ground ruins
    this.layers.push({
      color: '#5A5A5A',
      scrollSpeed: 0.6,
      elements: this.generateRuins()
    });

    // Foreground battlefield
    this.layers.push({
      color: '#8B4513',
      scrollSpeed: 1,
      elements: this.generateBattlefield()
    });
  }

  private generateClouds(): BackgroundElement[] {
    const clouds: BackgroundElement[] = [];
    for (let x = 0; x < this.width + 300; x += 200) {
      clouds.push({
        x,
        y: Math.random() * this.height * 0.3,
        width: 80 + Math.random() * 40,
        height: 30 + Math.random() * 20,
        type: 'cloud'
      });
    }
    return clouds;
  }

  private generateCityscape(): BackgroundElement[] {
    const buildings: BackgroundElement[] = [];
    for (let x = 0; x < this.width + 200; x += 60) {
      const buildingHeight = this.height * (0.3 + Math.random() * 0.3);
      buildings.push({
        x,
        y: this.height * 0.7 - buildingHeight,
        width: 50 + Math.random() * 30,
        height: buildingHeight,
        type: 'building'
      });
    }
    return buildings;
  }

  private generateRuins(): BackgroundElement[] {
    const ruins: BackgroundElement[] = [];
    for (let x = 0; x < this.width + 150; x += 80) {
      ruins.push({
        x,
        y: this.height * 0.7,
        width: 40 + Math.random() * 20,
        height: this.height * (0.1 + Math.random() * 0.2),
        type: 'ruin'
      });
    }
    return ruins;
  }

  private generateBattlefield(): BackgroundElement[] {
    const ground: BackgroundElement[] = [];
    for (let x = 0; x < this.width + 100; x += 50) {
      ground.push({
        x,
        y: this.height * 0.85,
        width: 50,
        height: this.height * 0.15,
        type: 'ground'
      });
    }
    return ground;
  }

  update() {
    this.layers.forEach(layer => {
      layer.elements.forEach(element => {
        element.x -= this.scrollSpeed * layer.scrollSpeed;
        
        // Reset position when element goes off screen
        if (element.x + element.width < 0) {
          element.x = this.width;
        }
      });
    });
  }

  render() {
    // Draw dark war-torn sky
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.height);
    gradient.addColorStop(0, '#2F2F2F');
    gradient.addColorStop(0.7, '#4A4A4A');
    gradient.addColorStop(1, '#8B4513');
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.width, this.height);

    // Draw each layer
    this.layers.forEach(layer => {
      layer.elements.forEach(element => {
        this.ctx.fillStyle = layer.color;
        
        switch (element.type) {
          case 'cloud':
            // Draw smoke/cloud
            this.ctx.save();
            this.ctx.globalAlpha = 0.6;
            this.ctx.beginPath();
            this.ctx.arc(element.x, element.y, element.width/4, 0, Math.PI * 2);
            this.ctx.arc(element.x + element.width/3, element.y, element.width/3, 0, Math.PI * 2);
            this.ctx.arc(element.x + element.width/2, element.y, element.width/4, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
            break;
            
          case 'building':
            // Draw damaged buildings
            this.ctx.fillRect(element.x, element.y, element.width, element.height);
            // Add damage/windows
            this.ctx.fillStyle = '#1C1C1C';
            for (let i = 0; i < 3; i++) {
              for (let j = 0; j < Math.floor(element.height / 20); j++) {
                this.ctx.fillRect(
                  element.x + 5 + (i * 15), 
                  element.y + 10 + (j * 20), 
                  8, 12
                );
              }
            }
            break;
            
          case 'ruin':
            // Draw broken structures
            this.ctx.fillRect(element.x, element.y, element.width, element.height);
            // Add broken top
            this.ctx.fillStyle = '#3A3A3A';
            this.ctx.beginPath();
            this.ctx.moveTo(element.x, element.y);
            this.ctx.lineTo(element.x + element.width/3, element.y - 10);
            this.ctx.lineTo(element.x + element.width, element.y + 5);
            this.ctx.lineTo(element.x + element.width, element.y);
            this.ctx.closePath();
            this.ctx.fill();
            break;
            
          case 'ground':
            // Draw battlefield ground
            this.ctx.fillRect(element.x, element.y, element.width, element.height);
            // Add dirt texture
            this.ctx.fillStyle = '#654321';
            for (let i = 0; i < 5; i++) {
              this.ctx.fillRect(
                element.x + Math.random() * element.width,
                element.y + Math.random() * element.height,
                3, 2
              );
            }
            break;
            
          default:
            this.ctx.fillRect(element.x, element.y, element.width, element.height);
        }
      });
    });
  }

  resize(width: number, height: number) {
    this.width = width;
    this.height = height;
    this.initializeLayers();
  }
}

interface BackgroundLayer {
  color: string;
  scrollSpeed: number;
  elements: BackgroundElement[];
}

interface BackgroundElement {
  x: number;
  y: number;
  width: number;
  height: number;
  type: string;
}
