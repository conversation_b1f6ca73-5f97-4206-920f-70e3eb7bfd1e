// Utility to generate placeholder sound effects using Web Audio API
export class SoundGenerator {
  private audioContext: AudioContext;
  
  constructor() {
    this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
  }
  
  // Generate a gunshot sound effect
  generateGunshot(): AudioBuffer {
    const sampleRate = this.audioContext.sampleRate;
    const duration = 0.3; // 300ms
    const length = sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, length, sampleRate);
    const data = buffer.getChannelData(0);
    
    for (let i = 0; i < length; i++) {
      const t = i / sampleRate;
      
      // Sharp attack with noise burst
      const attack = Math.exp(-t * 50);
      const noise = (Math.random() * 2 - 1) * attack;
      
      // Low frequency thump
      const thump = Math.sin(2 * Math.PI * 80 * t) * attack * 0.5;
      
      // High frequency crack
      const crack = Math.sin(2 * Math.PI * 2000 * t) * Math.exp(-t * 20) * 0.3;
      
      data[i] = (noise + thump + crack) * 0.7;
    }
    
    return buffer;
  }
  
  // Generate an explosion sound effect
  generateExplosion(): AudioBuffer {
    const sampleRate = this.audioContext.sampleRate;
    const duration = 1.5; // 1.5 seconds
    const length = sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, length, sampleRate);
    const data = buffer.getChannelData(0);
    
    for (let i = 0; i < length; i++) {
      const t = i / sampleRate;
      
      // Initial blast
      const blast = Math.exp(-t * 3) * (Math.random() * 2 - 1);
      
      // Rumble
      const rumble = Math.sin(2 * Math.PI * 40 * t) * Math.exp(-t * 1.5) * 0.6;
      
      // Crackling
      const crackle = (Math.random() * 2 - 1) * Math.exp(-t * 2) * 0.4;
      
      data[i] = (blast + rumble + crackle) * 0.8;
    }
    
    return buffer;
  }
  
  // Generate a footstep sound effect
  generateFootstep(): AudioBuffer {
    const sampleRate = this.audioContext.sampleRate;
    const duration = 0.2; // 200ms
    const length = sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, length, sampleRate);
    const data = buffer.getChannelData(0);
    
    for (let i = 0; i < length; i++) {
      const t = i / sampleRate;
      
      // Thud sound
      const thud = Math.sin(2 * Math.PI * 120 * t) * Math.exp(-t * 15);
      
      // Dirt/gravel texture
      const texture = (Math.random() * 2 - 1) * Math.exp(-t * 10) * 0.3;
      
      data[i] = (thud + texture) * 0.5;
    }
    
    return buffer;
  }
  
  // Generate a reload sound effect
  generateReload(): AudioBuffer {
    const sampleRate = this.audioContext.sampleRate;
    const duration = 0.8; // 800ms
    const length = sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, length, sampleRate);
    const data = buffer.getChannelData(0);
    
    for (let i = 0; i < length; i++) {
      const t = i / sampleRate;
      
      // Mechanical clicks and clacks
      let sound = 0;
      
      // Magazine out (0.1s)
      if (t < 0.1) {
        sound += Math.sin(2 * Math.PI * 800 * t) * Math.exp(-t * 20) * 0.3;
      }
      
      // Magazine in (0.4-0.6s)
      if (t > 0.4 && t < 0.6) {
        const localT = t - 0.4;
        sound += Math.sin(2 * Math.PI * 600 * localT) * Math.exp(-localT * 15) * 0.4;
      }
      
      // Bolt action (0.7s)
      if (t > 0.7) {
        const localT = t - 0.7;
        sound += Math.sin(2 * Math.PI * 1000 * localT) * Math.exp(-localT * 30) * 0.5;
      }
      
      data[i] = sound * 0.6;
    }
    
    return buffer;
  }
  
  // Generate ambient wind sound
  generateAmbientWind(): AudioBuffer {
    const sampleRate = this.audioContext.sampleRate;
    const duration = 10; // 10 seconds (will be looped)
    const length = sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, length, sampleRate);
    const data = buffer.getChannelData(0);
    
    for (let i = 0; i < length; i++) {
      const t = i / sampleRate;
      
      // Low frequency wind
      const wind1 = Math.sin(2 * Math.PI * 0.5 * t) * 0.2;
      const wind2 = Math.sin(2 * Math.PI * 0.3 * t) * 0.15;
      
      // High frequency whistle
      const whistle = Math.sin(2 * Math.PI * 800 * t) * Math.sin(2 * Math.PI * 0.1 * t) * 0.05;
      
      // Random gusts
      const gust = (Math.random() * 2 - 1) * 0.1 * Math.sin(2 * Math.PI * 0.05 * t);
      
      data[i] = wind1 + wind2 + whistle + gust;
    }
    
    return buffer;
  }
  
  // Generate helicopter rotor sound
  generateHelicopterRotor(): AudioBuffer {
    const sampleRate = this.audioContext.sampleRate;
    const duration = 5; // 5 seconds (will be looped)
    const length = sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, length, sampleRate);
    const data = buffer.getChannelData(0);
    
    for (let i = 0; i < length; i++) {
      const t = i / sampleRate;
      
      // Main rotor (low frequency)
      const mainRotor = Math.sin(2 * Math.PI * 8 * t) * 0.4;
      
      // Tail rotor (higher frequency)
      const tailRotor = Math.sin(2 * Math.PI * 40 * t) * 0.2;
      
      // Engine noise
      const engine = (Math.random() * 2 - 1) * 0.1;
      
      // Doppler effect simulation
      const doppler = Math.sin(2 * Math.PI * 0.2 * t) * 0.1;
      
      data[i] = (mainRotor + tailRotor + engine) * (1 + doppler);
    }
    
    return buffer;
  }
  
  // Generate tank engine sound
  generateTankEngine(): AudioBuffer {
    const sampleRate = this.audioContext.sampleRate;
    const duration = 8; // 8 seconds (will be looped)
    const length = sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, length, sampleRate);
    const data = buffer.getChannelData(0);
    
    for (let i = 0; i < length; i++) {
      const t = i / sampleRate;
      
      // Engine rumble (low frequency)
      const rumble = Math.sin(2 * Math.PI * 30 * t) * 0.5;
      
      // Engine vibration
      const vibration = Math.sin(2 * Math.PI * 60 * t) * 0.3;
      
      // Mechanical noise
      const mechanical = (Math.random() * 2 - 1) * 0.2;
      
      // Track noise
      const tracks = Math.sin(2 * Math.PI * 4 * t) * 0.1;
      
      data[i] = rumble + vibration + mechanical + tracks;
    }
    
    return buffer;
  }
  
  // Generate enemy death sound
  generateEnemyDeath(): AudioBuffer {
    const sampleRate = this.audioContext.sampleRate;
    const duration = 1.0; // 1 second
    const length = sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, length, sampleRate);
    const data = buffer.getChannelData(0);
    
    for (let i = 0; i < length; i++) {
      const t = i / sampleRate;
      
      // Scream/yell (simplified)
      const scream = Math.sin(2 * Math.PI * 400 * t) * Math.exp(-t * 2) * 0.6;
      
      // Body fall
      const fall = Math.sin(2 * Math.PI * 80 * t) * Math.exp(-t * 3) * 0.4;
      
      data[i] = scream + fall;
    }
    
    return buffer;
  }
  
  // Convert AudioBuffer to downloadable blob
  bufferToWav(buffer: AudioBuffer): Blob {
    const length = buffer.length;
    const arrayBuffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(arrayBuffer);
    const data = buffer.getChannelData(0);
    
    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };
    
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, buffer.sampleRate, true);
    view.setUint32(28, buffer.sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * 2, true);
    
    // Convert float samples to 16-bit PCM
    let offset = 44;
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, data[i]));
      view.setInt16(offset, sample * 0x7FFF, true);
      offset += 2;
    }
    
    return new Blob([arrayBuffer], { type: 'audio/wav' });
  }
}
