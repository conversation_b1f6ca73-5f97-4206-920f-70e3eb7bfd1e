import { BulletData, Vector2 } from './types';

export class Bullet implements BulletData {
  position: Vector2;
  velocity: Vector2;
  width: number = 8;
  height: number = 3;
  active: boolean = true;
  damage: number;
  isPlayerBullet: boolean;
  id: string;
  
  private speed: number = 500; // pixels per second
  private canvasWidth: number;
  private canvasHeight: number;

  constructor(
    startPosition: Vector2,
    direction: Vector2,
    isPlayerBullet: boolean,
    canvasWidth: number,
    canvasHeight: number,
    damage: number = 25
  ) {
    this.position = { ...startPosition };
    this.isPlayerBullet = isPlayerBullet;
    this.damage = damage;
    this.canvasWidth = canvasWidth;
    this.canvasHeight = canvasHeight;
    this.id = Math.random().toString(36).substr(2, 9);

    // Normalize direction and apply speed
    const magnitude = Math.sqrt(direction.x * direction.x + direction.y * direction.y);
    if (magnitude > 0) {
      this.velocity = {
        x: (direction.x / magnitude) * this.speed,
        y: (direction.y / magnitude) * this.speed
      };
    } else {
      // Default direction (right for player, left for enemies)
      this.velocity = {
        x: isPlayerBullet ? this.speed : -this.speed,
        y: 0
      };
    }
  }

  update(deltaTime: number) {
    if (!this.active) return;

    // Update position
    this.position.x += this.velocity.x * deltaTime;
    this.position.y += this.velocity.y * deltaTime;

    // Deactivate if off screen
    if (
      this.position.x < -this.width ||
      this.position.x > this.canvasWidth + this.width ||
      this.position.y < -this.height ||
      this.position.y > this.canvasHeight + this.height
    ) {
      this.active = false;
    }
  }

  render(ctx: CanvasRenderingContext2D) {
    if (!this.active) return;

    ctx.save();

    if (this.isPlayerBullet) {
      // Player bullet - bright yellow energy blast
      ctx.fillStyle = '#FFFF00';
      ctx.fillRect(this.position.x, this.position.y, this.width, this.height);
      
      // Add energy trail
      ctx.fillStyle = '#FFD700';
      ctx.fillRect(this.position.x - 4, this.position.y + 1, 4, 1);
      
      // Glow effect
      ctx.shadowColor = '#FFFF00';
      ctx.shadowBlur = 8;
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(this.position.x + 1, this.position.y, this.width - 2, this.height);
    } else {
      // Enemy bullet - red projectile
      ctx.fillStyle = '#FF0000';
      ctx.fillRect(this.position.x, this.position.y, this.width, this.height);
      
      // Add darker core
      ctx.fillStyle = '#AA0000';
      ctx.fillRect(this.position.x + 1, this.position.y, this.width - 2, this.height);
      
      // Glow effect
      ctx.shadowColor = '#FF4444';
      ctx.shadowBlur = 6;
      ctx.fillStyle = '#FF6666';
      ctx.fillRect(this.position.x, this.position.y, this.width, this.height);
    }

    ctx.restore();
  }

  hit() {
    this.active = false;
  }

  getPosition(): Vector2 {
    return { ...this.position };
  }

  getBounds() {
    return {
      x: this.position.x,
      y: this.position.y,
      width: this.width,
      height: this.height
    };
  }
}
