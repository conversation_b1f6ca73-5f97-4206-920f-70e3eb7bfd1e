{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push"}, "dependencies": {"@fontsource/inter": "^5.2.5", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@react-three/postprocessing": "^2.19.1", "@tanstack/react-query": "^5.60.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "connect-pg-simple": "^10.0.0", "date-fns": "^3.6.0", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.3.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "gl-matrix": "^3.4.3", "gsap": "^3.12.5", "howler": "^2.2.4", "input-otp": "^1.2.4", "lucide-react": "^0.453.0", "matter-js": "^0.20.0", "memorystore": "^1.6.7", "meshline": "^3.3.1", "next-themes": "^0.4.5", "ogl": "^1.0.11", "passport": "^0.7.0", "passport-local": "^1.0.0", "pixi.js": "^8.8.1", "postprocessing": "^6.36.0", "r3f-perf": "^7.2.3", "react": "^18.3.1", "react-confetti": "^6.4.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-haiku": "^2.2.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.1", "react-icons": "^5.4.0", "react-leaflet": "^4.2.1", "react-resizable-panels": "^2.1.4", "react-router-dom": "^6.26.0", "react-syntax-highlighter": "^15.5.0", "react-use-gesture": "^9.1.3", "react-useanimations": "^2.10.0", "recharts": "^2.13.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.4", "tailwind-scrollbar": "^3.1.0", "tailwindcss-animate": "^1.0.7", "three": "^0.170.0", "vaul": "^1.1.0", "vite-plugin-glsl": "^1.3.1", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.23.8", "zod-validation-error": "^3.4.0", "zustand": "^5.0.3"}, "devDependencies": {"@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.19"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}