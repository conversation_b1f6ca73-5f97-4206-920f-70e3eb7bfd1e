import * as THREE from 'three';
import { useAudio } from '../stores/useAudio';
import { SoundGenerator } from '../utils/soundGenerator';

export class AudioManager3D {
  private listener: THREE.AudioListener;
  private camera: THREE.Camera;
  private audioLoader: THREE.AudioLoader;
  
  // Audio sources
  private backgroundMusic: THREE.Audio | null = null;
  private ambientSounds: THREE.PositionalAudio[] = [];
  
  // Sound pools for performance
  private gunShotSounds: THREE.PositionalAudio[] = [];
  private explosionSounds: THREE.PositionalAudio[] = [];
  private hitSounds: THREE.PositionalAudio[] = [];
  private footstepSounds: THREE.PositionalAudio[] = [];
  
  // Audio buffers
  private audioBuffers: Map<string, AudioBuffer> = new Map();
  
  // Settings
  private masterVolume: number = 1.0;
  private musicVolume: number = 0.3;
  private sfxVolume: number = 0.7;
  private isMuted: boolean = false;
  
  constructor(camera: THREE.Camera) {
    this.camera = camera;
    this.listener = new THREE.AudioListener();
    this.audioLoader = new THREE.AudioLoader();

    // Add listener to camera
    camera.add(this.listener);

    this.loadAudioAssets();
  }
  
  private async loadAudioAssets() {
    const audioFiles = {
      backgroundMusic: '/sounds/background.mp3',
      gunshot: '/sounds/gunshot.mp3',
      explosion: '/sounds/explosion.mp3',
      hit: '/sounds/hit.mp3',
      footstep: '/sounds/footstep.mp3',
      reload: '/sounds/reload.mp3',
      enemyDeath: '/sounds/enemy-death.mp3',
      ambientWind: '/sounds/ambient-wind.mp3',
      helicopterRotor: '/sounds/helicopter.mp3',
      tankEngine: '/sounds/tank-engine.mp3'
    };

    const soundGenerator = new SoundGenerator();

    // Load all audio files with generated fallbacks
    for (const [name, url] of Object.entries(audioFiles)) {
      try {
        const buffer = await this.loadAudioBuffer(url);
        this.audioBuffers.set(name, buffer);
      } catch (error) {
        console.warn(`Failed to load audio: ${url}, using generated sound`, error);

        // Generate fallback sounds
        let fallbackBuffer: AudioBuffer;
        switch (name) {
          case 'gunshot':
            fallbackBuffer = soundGenerator.generateGunshot();
            break;
          case 'explosion':
            fallbackBuffer = soundGenerator.generateExplosion();
            break;
          case 'footstep':
            fallbackBuffer = soundGenerator.generateFootstep();
            break;
          case 'reload':
            fallbackBuffer = soundGenerator.generateReload();
            break;
          case 'enemyDeath':
            fallbackBuffer = soundGenerator.generateEnemyDeath();
            break;
          case 'ambientWind':
            fallbackBuffer = soundGenerator.generateAmbientWind();
            break;
          case 'helicopterRotor':
            fallbackBuffer = soundGenerator.generateHelicopterRotor();
            break;
          case 'tankEngine':
            fallbackBuffer = soundGenerator.generateTankEngine();
            break;
          default:
            // Create a silent buffer as final fallback
            const context = THREE.AudioContext.getContext();
            fallbackBuffer = context.createBuffer(1, 1, 22050);
            break;
        }

        this.audioBuffers.set(name, fallbackBuffer);
      }
    }

    // Initialize sound pools and ambient sounds after loading
    this.createSoundPools();
    this.setupAmbientSounds();

    console.log('Audio assets loaded with generated fallbacks');
  }
  
  private loadAudioBuffer(url: string): Promise<AudioBuffer> {
    return new Promise((resolve, reject) => {
      this.audioLoader.load(
        url,
        (buffer) => resolve(buffer),
        undefined,
        (error) => reject(error)
      );
    });
  }
  
  private createSoundPools() {
    // Create pools of reusable audio objects for performance
    const poolSize = 10;
    
    // Gunshot sounds pool
    for (let i = 0; i < poolSize; i++) {
      const sound = new THREE.PositionalAudio(this.listener);
      const buffer = this.audioBuffers.get('gunshot');
      if (buffer) {
        sound.setBuffer(buffer);
        sound.setVolume(this.sfxVolume * 0.8);
        sound.setRefDistance(5);
        sound.setRolloffFactor(2);
      }
      this.gunShotSounds.push(sound);
    }
    
    // Explosion sounds pool
    for (let i = 0; i < poolSize; i++) {
      const sound = new THREE.PositionalAudio(this.listener);
      const buffer = this.audioBuffers.get('explosion');
      if (buffer) {
        sound.setBuffer(buffer);
        sound.setVolume(this.sfxVolume);
        sound.setRefDistance(15);
        sound.setRolloffFactor(1);
      }
      this.explosionSounds.push(sound);
    }
    
    // Hit sounds pool
    for (let i = 0; i < poolSize; i++) {
      const sound = new THREE.PositionalAudio(this.listener);
      const buffer = this.audioBuffers.get('hit');
      if (buffer) {
        sound.setBuffer(buffer);
        sound.setVolume(this.sfxVolume * 0.6);
        sound.setRefDistance(3);
        sound.setRolloffFactor(3);
      }
      this.hitSounds.push(sound);
    }
    
    // Footstep sounds pool
    for (let i = 0; i < 5; i++) {
      const sound = new THREE.PositionalAudio(this.listener);
      const buffer = this.audioBuffers.get('footstep');
      if (buffer) {
        sound.setBuffer(buffer);
        sound.setVolume(this.sfxVolume * 0.4);
        sound.setRefDistance(2);
        sound.setRolloffFactor(4);
      }
      this.footstepSounds.push(sound);
    }
  }
  
  private setupAmbientSounds() {
    // Create ambient wind sound
    const windBuffer = this.audioBuffers.get('ambientWind');
    if (windBuffer) {
      const windSound = new THREE.Audio(this.listener);
      windSound.setBuffer(windBuffer);
      windSound.setLoop(true);
      windSound.setVolume(this.sfxVolume * 0.2);
      windSound.play();
      this.ambientSounds.push(windSound);
    }
  }
  
  playBackgroundMusic() {
    if (this.isMuted) return;
    
    const buffer = this.audioBuffers.get('backgroundMusic');
    if (buffer && !this.backgroundMusic) {
      this.backgroundMusic = new THREE.Audio(this.listener);
      this.backgroundMusic.setBuffer(buffer);
      this.backgroundMusic.setLoop(true);
      this.backgroundMusic.setVolume(this.musicVolume);
      this.backgroundMusic.play();
    }
  }
  
  stopBackgroundMusic() {
    if (this.backgroundMusic && this.backgroundMusic.isPlaying) {
      this.backgroundMusic.stop();
    }
  }
  
  playGunshot(position: THREE.Vector3) {
    if (this.isMuted) return;
    
    const sound = this.getAvailableSound(this.gunShotSounds);
    if (sound) {
      this.playPositionalSound(sound, position);
    }
  }
  
  playExplosion(position: THREE.Vector3) {
    if (this.isMuted) return;
    
    const sound = this.getAvailableSound(this.explosionSounds);
    if (sound) {
      this.playPositionalSound(sound, position);
      
      // Add screen shake effect (could be implemented in camera controller)
      this.createScreenShake();
    }
  }
  
  playHit(position: THREE.Vector3) {
    if (this.isMuted) return;
    
    const sound = this.getAvailableSound(this.hitSounds);
    if (sound) {
      this.playPositionalSound(sound, position);
    }
  }
  
  playFootstep(position: THREE.Vector3) {
    if (this.isMuted) return;
    
    const sound = this.getAvailableSound(this.footstepSounds);
    if (sound) {
      this.playPositionalSound(sound, position);
    }
  }
  
  playEnemyDeath(position: THREE.Vector3) {
    if (this.isMuted) return;
    
    const buffer = this.audioBuffers.get('enemyDeath');
    if (buffer) {
      const sound = new THREE.PositionalAudio(this.listener);
      sound.setBuffer(buffer);
      sound.setVolume(this.sfxVolume * 0.8);
      sound.setRefDistance(8);
      this.playPositionalSound(sound, position);
    }
  }
  
  playHelicopterRotor(helicopterMesh: THREE.Object3D) {
    if (this.isMuted) return;
    
    const buffer = this.audioBuffers.get('helicopterRotor');
    if (buffer) {
      const sound = new THREE.PositionalAudio(this.listener);
      sound.setBuffer(buffer);
      sound.setLoop(true);
      sound.setVolume(this.sfxVolume * 0.6);
      sound.setRefDistance(20);
      helicopterMesh.add(sound);
      sound.play();
    }
  }
  
  playTankEngine(tankMesh: THREE.Object3D) {
    if (this.isMuted) return;
    
    const buffer = this.audioBuffers.get('tankEngine');
    if (buffer) {
      const sound = new THREE.PositionalAudio(this.listener);
      sound.setBuffer(buffer);
      sound.setLoop(true);
      sound.setVolume(this.sfxVolume * 0.5);
      sound.setRefDistance(15);
      tankMesh.add(sound);
      sound.play();
    }
  }
  
  private getAvailableSound(pool: THREE.PositionalAudio[]): THREE.PositionalAudio | null {
    for (const sound of pool) {
      if (!sound.isPlaying) {
        return sound;
      }
    }
    return null; // All sounds in pool are playing
  }
  
  private playPositionalSound(sound: THREE.PositionalAudio, position: THREE.Vector3) {
    // Create a temporary object to hold the sound at the position
    const soundObject = new THREE.Object3D();
    soundObject.position.copy(position);
    soundObject.add(sound);
    
    // Add to scene temporarily
    this.camera.parent?.add(soundObject);
    
    sound.play();
    
    // Remove after sound finishes
    sound.onEnded = () => {
      soundObject.remove(sound);
      this.camera.parent?.remove(soundObject);
    };
  }
  
  private createScreenShake() {
    // This would be implemented in the camera controller
    // For now, just log the effect
    console.log('Screen shake effect triggered');
  }
  
  setMasterVolume(volume: number) {
    this.masterVolume = Math.max(0, Math.min(1, volume));
    this.updateAllVolumes();
  }
  
  setMusicVolume(volume: number) {
    this.musicVolume = Math.max(0, Math.min(1, volume));
    if (this.backgroundMusic) {
      this.backgroundMusic.setVolume(this.musicVolume * this.masterVolume);
    }
  }
  
  setSFXVolume(volume: number) {
    this.sfxVolume = Math.max(0, Math.min(1, volume));
    this.updateAllVolumes();
  }
  
  toggleMute() {
    this.isMuted = !this.isMuted;
    
    if (this.isMuted) {
      this.stopAllSounds();
    } else {
      this.playBackgroundMusic();
    }
  }
  
  private updateAllVolumes() {
    const effectiveVolume = this.masterVolume;
    
    if (this.backgroundMusic) {
      this.backgroundMusic.setVolume(this.musicVolume * effectiveVolume);
    }
    
    // Update sound pools
    [...this.gunShotSounds, ...this.explosionSounds, ...this.hitSounds, ...this.footstepSounds]
      .forEach(sound => {
        sound.setVolume(this.sfxVolume * effectiveVolume);
      });
    
    this.ambientSounds.forEach(sound => {
      sound.setVolume(this.sfxVolume * effectiveVolume * 0.3);
    });
  }
  
  private stopAllSounds() {
    if (this.backgroundMusic && this.backgroundMusic.isPlaying) {
      this.backgroundMusic.pause();
    }
    
    this.ambientSounds.forEach(sound => {
      if (sound.isPlaying) {
        sound.pause();
      }
    });
  }
  
  cleanup() {
    this.stopAllSounds();
    
    // Dispose of all audio objects
    if (this.backgroundMusic) {
      this.backgroundMusic.disconnect();
    }
    
    [...this.gunShotSounds, ...this.explosionSounds, ...this.hitSounds, ...this.footstepSounds]
      .forEach(sound => sound.disconnect());
    
    this.ambientSounds.forEach(sound => sound.disconnect());
  }
}
