import * as THREE from 'three';

export class Environment3D {
  private scene: THREE.Scene;
  private terrain: THREE.Mesh;
  private buildings: THREE.Group[] = [];
  private obstacles: THREE.Mesh[] = [];
  private skybox: THREE.Mesh;
  
  // Scrolling elements
  private scrollSpeed: number = 5;
  private terrainSegments: THREE.Mesh[] = [];
  private buildingSegments: THREE.Group[] = [];
  
  constructor(scene: THREE.Scene) {
    this.scene = scene;
    
    this.createSkybox();
    this.createTerrain();
    this.createBuildings();
    this.createObstacles();
    this.addAtmosphericEffects();
  }
  
  private createSkybox() {
    // Create a large sphere for the sky
    const skyGeometry = new THREE.SphereGeometry(500, 32, 32);
    
    // Create gradient texture for war-torn sky
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 512;
    const context = canvas.getContext('2d')!;
    
    // Create gradient from dark gray to reddish brown
    const gradient = context.createLinearGradient(0, 0, 0, 512);
    gradient.addColorStop(0, '#2F2F2F'); // Dark gray at top
    gradient.addColorStop(0.7, '#4A4A4A'); // Medium gray
    gradient.addColorStop(1, '#8B4513'); // Brown at bottom
    
    context.fillStyle = gradient;
    context.fillRect(0, 0, 512, 512);
    
    // Add some cloud-like noise
    for (let i = 0; i < 100; i++) {
      context.fillStyle = `rgba(100, 100, 100, ${Math.random() * 0.3})`;
      context.beginPath();
      context.arc(
        Math.random() * 512,
        Math.random() * 256,
        Math.random() * 20 + 5,
        0,
        Math.PI * 2
      );
      context.fill();
    }
    
    const skyTexture = new THREE.CanvasTexture(canvas);
    const skyMaterial = new THREE.MeshBasicMaterial({ 
      map: skyTexture,
      side: THREE.BackSide
    });
    
    this.skybox = new THREE.Mesh(skyGeometry, skyMaterial);
    this.scene.add(this.skybox);
  }
  
  private createTerrain() {
    // Create multiple terrain segments for infinite scrolling
    for (let i = 0; i < 5; i++) {
      const terrainSegment = this.createTerrainSegment();
      terrainSegment.position.z = -100 + (i * 50);
      this.terrainSegments.push(terrainSegment);
      this.scene.add(terrainSegment);
    }
  }
  
  private createTerrainSegment(): THREE.Mesh {
    const geometry = new THREE.PlaneGeometry(100, 50, 32, 16);
    
    // Add some height variation to make it more realistic
    const positions = geometry.attributes.position.array as Float32Array;
    for (let i = 0; i < positions.length; i += 3) {
      positions[i + 1] = Math.random() * 2 - 1; // Y coordinate (height)
    }
    geometry.attributes.position.needsUpdate = true;
    geometry.computeVertexNormals();
    
    // Create terrain texture
    const canvas = document.createElement('canvas');
    canvas.width = 256;
    canvas.height = 256;
    const context = canvas.getContext('2d')!;
    
    // Base dirt color
    context.fillStyle = '#8B4513';
    context.fillRect(0, 0, 256, 256);
    
    // Add dirt texture
    for (let i = 0; i < 1000; i++) {
      context.fillStyle = `rgba(${101 + Math.random() * 40}, ${67 + Math.random() * 20}, ${19 + Math.random() * 10}, 0.5)`;
      context.fillRect(
        Math.random() * 256,
        Math.random() * 256,
        Math.random() * 3 + 1,
        Math.random() * 3 + 1
      );
    }
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    texture.repeat.set(4, 2);
    
    const material = new THREE.MeshLambertMaterial({ 
      map: texture,
      side: THREE.DoubleSide
    });
    
    const terrain = new THREE.Mesh(geometry, material);
    terrain.rotation.x = -Math.PI / 2;
    terrain.receiveShadow = true;
    
    return terrain;
  }
  
  private createBuildings() {
    // Create building segments for scrolling
    for (let segment = 0; segment < 3; segment++) {
      const buildingGroup = new THREE.Group();
      
      // Create buildings in this segment
      for (let i = 0; i < 8; i++) {
        const building = this.createBuilding();
        building.position.set(
          (Math.random() - 0.5) * 80, // X position
          0, // Y position (on ground)
          -60 + (segment * 60) + (Math.random() - 0.5) * 20 // Z position
        );
        buildingGroup.add(building);
      }
      
      this.buildingSegments.push(buildingGroup);
      this.scene.add(buildingGroup);
    }
  }
  
  private createBuilding(): THREE.Group {
    const building = new THREE.Group();
    
    // Building dimensions
    const width = 5 + Math.random() * 10;
    const height = 10 + Math.random() * 20;
    const depth = 5 + Math.random() * 8;
    
    // Main building structure
    const buildingGeometry = new THREE.BoxGeometry(width, height, depth);
    const buildingMaterial = new THREE.MeshLambertMaterial({ 
      color: new THREE.Color().setHSL(0, 0, 0.2 + Math.random() * 0.3)
    });
    const buildingMesh = new THREE.Mesh(buildingGeometry, buildingMaterial);
    buildingMesh.position.y = height / 2;
    buildingMesh.castShadow = true;
    buildingMesh.receiveShadow = true;
    building.add(buildingMesh);
    
    // Add windows
    const windowMaterial = new THREE.MeshBasicMaterial({ 
      color: Math.random() > 0.7 ? 0xffff88 : 0x000000 // Some windows lit
    });
    
    const windowsPerFloor = Math.floor(width / 2);
    const floors = Math.floor(height / 3);
    
    for (let floor = 0; floor < floors; floor++) {
      for (let win = 0; win < windowsPerFloor; win++) {
        if (Math.random() > 0.3) { // Not all windows present (damage)
          const windowGeometry = new THREE.PlaneGeometry(0.8, 1.2);
          const window = new THREE.Mesh(windowGeometry, windowMaterial);
          window.position.set(
            -width/2 + 1 + (win * 2),
            3 + (floor * 3),
            depth/2 + 0.01
          );
          building.add(window);
        }
      }
    }
    
    // Add damage/destruction effects
    if (Math.random() > 0.6) {
      // Add rubble at base
      for (let i = 0; i < 5; i++) {
        const rubbleGeometry = new THREE.BoxGeometry(
          Math.random() * 2,
          Math.random() * 1,
          Math.random() * 2
        );
        const rubbleMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
        const rubble = new THREE.Mesh(rubbleGeometry, rubbleMaterial);
        rubble.position.set(
          (Math.random() - 0.5) * width * 1.5,
          Math.random() * 0.5,
          (Math.random() - 0.5) * depth * 1.5
        );
        rubble.rotation.set(
          Math.random() * Math.PI,
          Math.random() * Math.PI,
          Math.random() * Math.PI
        );
        rubble.castShadow = true;
        building.add(rubble);
      }
    }
    
    return building;
  }
  
  private createObstacles() {
    // Create various battlefield obstacles
    for (let i = 0; i < 20; i++) {
      const obstacle = this.createObstacle();
      obstacle.position.set(
        (Math.random() - 0.5) * 60,
        0,
        Math.random() * -100 - 20
      );
      this.obstacles.push(obstacle);
      this.scene.add(obstacle);
    }
  }
  
  private createObstacle(): THREE.Mesh {
    const obstacleTypes = ['barrel', 'crate', 'sandbag', 'debris'];
    const type = obstacleTypes[Math.floor(Math.random() * obstacleTypes.length)];
    
    let geometry: THREE.BufferGeometry;
    let material: THREE.Material;
    
    switch (type) {
      case 'barrel':
        geometry = new THREE.CylinderGeometry(0.5, 0.5, 1.5, 8);
        material = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        break;
        
      case 'crate':
        geometry = new THREE.BoxGeometry(1.5, 1.5, 1.5);
        material = new THREE.MeshLambertMaterial({ color: 0x654321 });
        break;
        
      case 'sandbag':
        geometry = new THREE.BoxGeometry(2, 0.8, 1);
        material = new THREE.MeshLambertMaterial({ color: 0xD2B48C });
        break;
        
      default: // debris
        geometry = new THREE.BoxGeometry(
          Math.random() * 2 + 0.5,
          Math.random() * 1 + 0.2,
          Math.random() * 2 + 0.5
        );
        material = new THREE.MeshLambertMaterial({ color: 0x555555 });
        break;
    }
    
    const obstacle = new THREE.Mesh(geometry, material);
    obstacle.position.y = 0.5;
    obstacle.castShadow = true;
    obstacle.receiveShadow = true;
    
    // Random rotation
    obstacle.rotation.y = Math.random() * Math.PI * 2;
    
    return obstacle;
  }
  
  private addAtmosphericEffects() {
    // Add volumetric fog
    this.scene.fog = new THREE.FogExp2(0x2F2F2F, 0.002);

    // Add atmospheric particles (dust, smoke, debris)
    this.createAtmosphericParticles();

    // Add distant explosions/flashes
    this.createDistantEffects();

    // Add searchlights
    this.createSearchlights();
  }

  private createAtmosphericParticles() {
    // Create floating dust particles
    const dustGeometry = new THREE.BufferGeometry();
    const dustCount = 500;
    const positions = new Float32Array(dustCount * 3);
    const colors = new Float32Array(dustCount * 3);

    for (let i = 0; i < dustCount; i++) {
      const i3 = i * 3;

      // Random positions in a large area
      positions[i3] = (Math.random() - 0.5) * 200;
      positions[i3 + 1] = Math.random() * 30;
      positions[i3 + 2] = (Math.random() - 0.5) * 200;

      // Dust color variations
      const dustColor = new THREE.Color(0x8B7355);
      dustColor.multiplyScalar(0.5 + Math.random() * 0.5);
      colors[i3] = dustColor.r;
      colors[i3 + 1] = dustColor.g;
      colors[i3 + 2] = dustColor.b;
    }

    dustGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    dustGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

    const dustMaterial = new THREE.PointsMaterial({
      size: 0.5,
      vertexColors: true,
      transparent: true,
      opacity: 0.3,
      blending: THREE.AdditiveBlending
    });

    const dustParticles = new THREE.Points(dustGeometry, dustMaterial);
    this.scene.add(dustParticles);

    // Animate dust particles
    const animateDust = () => {
      const positions = dustParticles.geometry.attributes.position.array as Float32Array;

      for (let i = 0; i < dustCount; i++) {
        const i3 = i * 3;

        // Slow floating motion
        positions[i3] += (Math.random() - 0.5) * 0.02;
        positions[i3 + 1] += Math.sin(Date.now() * 0.001 + i) * 0.01;
        positions[i3 + 2] += (Math.random() - 0.5) * 0.01;

        // Reset particles that go too far
        if (positions[i3] > 100) positions[i3] = -100;
        if (positions[i3] < -100) positions[i3] = 100;
        if (positions[i3 + 2] > 100) positions[i3 + 2] = -100;
        if (positions[i3 + 2] < -100) positions[i3 + 2] = 100;
      }

      dustParticles.geometry.attributes.position.needsUpdate = true;
      requestAnimationFrame(animateDust);
    };

    animateDust();
  }

  private createDistantEffects() {
    // Create distant explosion flashes
    setInterval(() => {
      if (Math.random() < 0.1) {
        const flash = new THREE.PointLight(0xff4400, 2, 50);
        flash.position.set(
          (Math.random() - 0.5) * 300,
          Math.random() * 20 + 5,
          -100 - Math.random() * 100
        );

        this.scene.add(flash);

        // Fade out the flash
        let intensity = 2;
        const fadeInterval = setInterval(() => {
          intensity *= 0.8;
          flash.intensity = intensity;

          if (intensity < 0.01) {
            this.scene.remove(flash);
            clearInterval(fadeInterval);
          }
        }, 50);
      }
    }, 3000);
  }

  private createSearchlights() {
    // Add moving searchlights for atmosphere
    for (let i = 0; i < 3; i++) {
      const spotlight = new THREE.SpotLight(0xffffff, 0.5, 100, Math.PI / 6, 0.5);
      spotlight.position.set(
        (Math.random() - 0.5) * 100,
        20 + Math.random() * 10,
        -50 - Math.random() * 50
      );

      spotlight.target.position.set(
        spotlight.position.x + (Math.random() - 0.5) * 20,
        0,
        spotlight.position.z + Math.random() * 30
      );

      spotlight.castShadow = true;
      spotlight.shadow.mapSize.width = 1024;
      spotlight.shadow.mapSize.height = 1024;

      this.scene.add(spotlight);
      this.scene.add(spotlight.target);

      // Animate searchlight movement
      const animateSearchlight = () => {
        const time = Date.now() * 0.001;
        spotlight.target.position.x = spotlight.position.x + Math.sin(time * 0.5 + i) * 30;
        spotlight.target.position.z = spotlight.position.z + Math.cos(time * 0.3 + i) * 20;

        // Flicker effect
        spotlight.intensity = 0.3 + Math.sin(time * 10 + i) * 0.1;

        requestAnimationFrame(animateSearchlight);
      };

      animateSearchlight();
    }
  }
  
  update(deltaTime: number) {
    // Scroll terrain segments
    this.terrainSegments.forEach(segment => {
      segment.position.z += this.scrollSpeed * deltaTime;

      // Reset position when segment goes too far
      if (segment.position.z > 50) {
        segment.position.z -= 250; // Move to back
      }
    });

    // Scroll building segments
    this.buildingSegments.forEach(segment => {
      segment.position.z += this.scrollSpeed * deltaTime;

      if (segment.position.z > 60) {
        segment.position.z -= 180;
      }
    });

    // Scroll obstacles
    this.obstacles.forEach(obstacle => {
      obstacle.position.z += this.scrollSpeed * deltaTime;

      if (obstacle.position.z > 30) {
        obstacle.position.z -= 150;
        obstacle.position.x = (Math.random() - 0.5) * 60;
      }
    });

    // Update weather effects
    this.updateWeatherEffects(deltaTime);
  }

  private updateWeatherEffects(deltaTime: number) {
    // Simulate wind effects on fog
    if (this.scene.fog && this.scene.fog instanceof THREE.FogExp2) {
      const time = Date.now() * 0.001;
      this.scene.fog.density = 0.002 + Math.sin(time * 0.5) * 0.0005;
    }

    // Random lightning flashes
    if (Math.random() < 0.001) {
      this.createLightningFlash();
    }
  }

  private createLightningFlash() {
    // Create brief lightning illumination
    const lightning = new THREE.DirectionalLight(0x9999ff, 2);
    lightning.position.set(
      (Math.random() - 0.5) * 200,
      50,
      (Math.random() - 0.5) * 200
    );

    this.scene.add(lightning);

    // Flash duration
    setTimeout(() => {
      this.scene.remove(lightning);
    }, 100 + Math.random() * 200);

    // Thunder sound would be played here via AudioManager3D
  }

  // Method to add dynamic weather
  setWeatherIntensity(intensity: number) {
    // Adjust fog density based on weather
    if (this.scene.fog && this.scene.fog instanceof THREE.FogExp2) {
      this.scene.fog.density = 0.001 + intensity * 0.003;
    }

    // Adjust ambient light based on weather
    const ambientLight = this.scene.children.find(child => child instanceof THREE.AmbientLight) as THREE.AmbientLight;
    if (ambientLight) {
      ambientLight.intensity = 0.3 * (1 - intensity * 0.5);
    }
  }

  // Method to trigger explosion effects in environment
  createEnvironmentExplosion(position: THREE.Vector3) {
    // Create crater in terrain (simplified)
    const crater = new THREE.Mesh(
      new THREE.RingGeometry(1, 3, 16),
      new THREE.MeshLambertMaterial({ color: 0x2F2F2F })
    );
    crater.position.copy(position);
    crater.position.y = 0.1;
    crater.rotation.x = -Math.PI / 2;
    this.scene.add(crater);

    // Remove crater after some time
    setTimeout(() => {
      this.scene.remove(crater);
    }, 30000);

    // Shake nearby objects
    this.obstacles.forEach(obstacle => {
      const distance = obstacle.position.distanceTo(position);
      if (distance < 10) {
        const shakeIntensity = (10 - distance) / 10;
        const originalPosition = obstacle.position.clone();

        // Shake animation
        let shakeTime = 0;
        const shakeInterval = setInterval(() => {
          shakeTime += 16;
          obstacle.position.x = originalPosition.x + (Math.random() - 0.5) * shakeIntensity;
          obstacle.position.z = originalPosition.z + (Math.random() - 0.5) * shakeIntensity;

          if (shakeTime > 500) {
            obstacle.position.copy(originalPosition);
            clearInterval(shakeInterval);
          }
        }, 16);
      }
    });
  }
}
