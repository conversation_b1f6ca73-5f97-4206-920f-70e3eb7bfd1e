import { useEffect, useState } from "react";
import "@fontsource/inter";
import GameCanvas from "./components/game/GameCanvas";
import GameMenu from "./components/game/GameMenu";
import GameUI from "./components/game/GameUI";
import SoundManager from "./components/game/SoundManager";
import { useContraGame } from "./lib/stores/useContraGame";

function App() {
  const { gameState } = useContraGame();
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Simple loading delay to ensure everything is ready
    const timer = setTimeout(() => setIsLoaded(true), 100);
    return () => clearTimeout(timer);
  }, []);

  if (!isLoaded) {
    return (
      <div className="w-full h-screen bg-gradient-to-br from-gray-900 via-black to-red-900 flex items-center justify-center relative overflow-hidden">
        {/* Loading background effects */}
        <div className="absolute inset-0">
          {/* Grid pattern */}
          <div
            className="absolute inset-0 opacity-10"
            style={{
              backgroundImage: `
                linear-gradient(rgba(0, 255, 0, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 0, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }}
          />

          {/* Scanlines */}
          <div className="absolute inset-0 scanline-effect opacity-20" />
        </div>

        <div className="text-center space-y-6 z-10">
          <div className="text-6xl font-black text-transparent bg-clip-text bg-gradient-to-r from-red-500 via-orange-500 to-yellow-500 font-mono tracking-widest">
            CONTRA
          </div>

          <div className="text-green-400 font-mono text-xl tracking-wider uppercase">
            Initializing Combat Systems...
          </div>

          {/* Loading bar */}
          <div className="w-64 h-2 bg-gray-800 border border-green-500 mx-auto">
            <div className="h-full bg-gradient-to-r from-green-500 to-green-400 animate-pulse" style={{ width: '100%' }} />
          </div>

          <div className="text-gray-400 font-mono text-sm">
            Loading Assets • Preparing Battlefield
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-screen bg-black overflow-hidden relative">
      <SoundManager />
      
      {gameState === 'menu' && <GameMenu />}
      
      {(gameState === 'playing' || gameState === 'gameOver') && (
        <>
          <GameCanvas />
          <GameUI />
        </>
      )}
    </div>
  );
}

export default App;
