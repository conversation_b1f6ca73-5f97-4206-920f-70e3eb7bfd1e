import * as THREE from 'three';
import { useContraGame } from '../stores/useContraGame';
import { useAudio } from '../stores/useAudio';
import { Player3D } from './Player3D';
import { Enemy3D } from './Enemy3D';
import { Environment3D } from './Environment3D';
import { ParticleSystem } from './ParticleSystem';
import { AudioManager3D } from './AudioManager3D';
import { PostProcessing } from './PostProcessing';
import { CameraController } from './CameraController';

export class Game3DEngine {
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;
  private clock: THREE.Clock;
  
  // Game objects
  private player: Player3D;
  private enemies: Enemy3D[] = [];
  private environment: Environment3D;
  private particleSystem: ParticleSystem;
  private audioManager: AudioManager3D;
  private cameraController: CameraController;
  
  // Game state
  private isRunning = false;
  private lastEnemySpawn = 0;
  private enemySpawnRate = 2000; // milliseconds
  
  // Lighting
  private ambientLight: THREE.AmbientLight;
  private directionalLight: THREE.DirectionalLight;
  private pointLights: THREE.PointLight[] = [];
  
  // Post-processing
  private postProcessing: PostProcessing;
  
  constructor(canvas: HTMLCanvasElement) {
    this.clock = new THREE.Clock();
    
    // Initialize Three.js scene
    this.scene = new THREE.Scene();
    this.scene.fog = new THREE.Fog(0x2F2F2F, 50, 200);
    
    // Setup camera
    this.camera = new THREE.PerspectiveCamera(
      75,
      canvas.clientWidth / canvas.clientHeight,
      0.1,
      1000
    );
    this.camera.position.set(0, 10, 20);
    this.camera.lookAt(0, 0, 0);
    
    // Setup renderer
    this.renderer = new THREE.WebGLRenderer({ 
      canvas,
      antialias: true,
      alpha: false
    });
    this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.renderer.outputColorSpace = THREE.SRGBColorSpace;
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
    this.renderer.toneMappingExposure = 1.2;
    
    // Setup lighting
    this.setupLighting();
    
    // Initialize game components
    this.environment = new Environment3D(this.scene);
    this.player = new Player3D(this.scene, this.camera);
    this.particleSystem = new ParticleSystem(this.scene);
    this.audioManager = new AudioManager3D(this.camera);

    // Connect audio manager to player
    this.player.setAudioManager(this.audioManager);

    // Initialize camera controller
    this.cameraController = new CameraController(this.camera, this.player);

    // Connect camera controller to player
    this.player.setCameraController(this.cameraController);

    // Initialize post-processing
    this.postProcessing = new PostProcessing(this.renderer, this.scene, this.camera);

    // Setup event listeners
    this.setupEventListeners();
    
    console.log('3D Game Engine initialized');
  }
  
  private setupLighting() {
    // Ambient light for general illumination
    this.ambientLight = new THREE.AmbientLight(0x404040, 0.3);
    this.scene.add(this.ambientLight);
    
    // Main directional light (sun)
    this.directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    this.directionalLight.position.set(50, 100, 50);
    this.directionalLight.castShadow = true;
    this.directionalLight.shadow.mapSize.width = 2048;
    this.directionalLight.shadow.mapSize.height = 2048;
    this.directionalLight.shadow.camera.near = 0.5;
    this.directionalLight.shadow.camera.far = 500;
    this.directionalLight.shadow.camera.left = -100;
    this.directionalLight.shadow.camera.right = 100;
    this.directionalLight.shadow.camera.top = 100;
    this.directionalLight.shadow.camera.bottom = -100;
    this.scene.add(this.directionalLight);
    
    // Add some atmospheric point lights
    const colors = [0xff4444, 0x44ff44, 0x4444ff];
    for (let i = 0; i < 3; i++) {
      const light = new THREE.PointLight(colors[i], 0.5, 30);
      light.position.set(
        (Math.random() - 0.5) * 100,
        5 + Math.random() * 10,
        (Math.random() - 0.5) * 100
      );
      this.pointLights.push(light);
      this.scene.add(light);
    }
  }
  
  private setupEventListeners() {
    // Handle window resize
    window.addEventListener('resize', this.handleResize.bind(this));
    
    // Handle keyboard input
    document.addEventListener('keydown', this.handleKeyDown.bind(this));
    document.addEventListener('keyup', this.handleKeyUp.bind(this));
  }
  
  private handleResize() {
    const canvas = this.renderer.domElement;
    this.camera.aspect = canvas.clientWidth / canvas.clientHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
  }
  
  private handleKeyDown(event: KeyboardEvent) {
    this.player.handleKeyDown(event);
  }
  
  private handleKeyUp(event: KeyboardEvent) {
    this.player.handleKeyUp(event);
  }
  
  start() {
    this.isRunning = true;
    this.lastEnemySpawn = Date.now();
    this.audioManager.playBackgroundMusic();
    this.animate();
    console.log('3D Game started');
  }
  
  stop() {
    this.isRunning = false;
    this.audioManager.stopBackgroundMusic();
  }
  
  private animate = () => {
    if (!this.isRunning) return;
    
    requestAnimationFrame(this.animate);
    
    const deltaTime = this.clock.getDelta();
    const elapsedTime = this.clock.getElapsedTime();
    
    this.update(deltaTime, elapsedTime);
    this.render();
  };
  
  private update(deltaTime: number, elapsedTime: number) {
    // Update game objects
    this.player.update(deltaTime);
    this.environment.update(deltaTime);
    this.particleSystem.update(deltaTime);
    this.postProcessing.update(deltaTime);
    this.cameraController.update(deltaTime);

    // Update enemies
    this.enemies.forEach(enemy => enemy.update(deltaTime));
    this.enemies = this.enemies.filter(enemy => enemy.isActive());

    // Spawn enemies
    this.spawnEnemies();

    // Update lighting effects
    this.updateLighting(elapsedTime);

    // Check collisions
    this.checkCollisions();
  }
  
  private spawnEnemies() {
    const now = Date.now();
    if (now - this.lastEnemySpawn > this.enemySpawnRate) {
      const enemy = new Enemy3D(this.scene);
      enemy.setPosition(
        Math.random() * 20 - 10,
        0,
        -50 - Math.random() * 20
      );
      this.enemies.push(enemy);
      this.lastEnemySpawn = now;
    }
  }
  
  private updateLighting(elapsedTime: number) {
    // Animate point lights
    this.pointLights.forEach((light, index) => {
      light.intensity = 0.3 + Math.sin(elapsedTime * 2 + index) * 0.2;

      // Move lights slightly for dynamic shadows
      light.position.x += Math.sin(elapsedTime * 0.5 + index) * 0.1;
      light.position.z += Math.cos(elapsedTime * 0.3 + index) * 0.1;
    });

    // Dynamic ambient lighting based on action
    const actionIntensity = this.enemies.length / 10; // More enemies = more intense lighting
    this.ambientLight.intensity = 0.2 + actionIntensity * 0.1;

    // Flicker main directional light occasionally
    if (Math.random() < 0.001) {
      const originalIntensity = this.directionalLight.intensity;
      this.directionalLight.intensity = originalIntensity * 0.5;
      setTimeout(() => {
        this.directionalLight.intensity = originalIntensity;
      }, 50 + Math.random() * 100);
    }
  }
  
  private checkCollisions() {
    // Player vs enemies
    const playerPosition = this.player.getPosition();
    this.enemies.forEach(enemy => {
      const enemyPosition = enemy.getPosition();
      const distance = playerPosition.distanceTo(enemyPosition);

      if (distance < 2) {
        // Handle collision
        this.player.takeDamage(10);
        enemy.destroy();
        this.particleSystem.createExplosion(enemyPosition);
        this.audioManager.playExplosion(enemyPosition);
        this.postProcessing.triggerDamageEffect(0.6);
        this.postProcessing.createScreenShake(0.8, 300);
        this.cameraController.triggerShake(1.0, 0.5);

        // Check if player died
        if (this.player.getHealth() <= 0) {
          useContraGame.getState().loseLife();
          if (useContraGame.getState().lives <= 0) {
            useContraGame.getState().setGameState('gameOver');
            this.stop();
          }
        }
      }
    });

    // Player bullets vs enemies
    const bullets = this.player.getBullets();
    bullets.forEach(bullet => {
      this.enemies.forEach(enemy => {
        if (bullet.checkCollision(enemy)) {
          const destroyed = enemy.takeDamage(25);
          bullet.destroy();
          this.particleSystem.createHitEffect(enemy.getPosition());
          this.audioManager.playHit(enemy.getPosition());

          if (destroyed) {
            // Enemy destroyed
            useContraGame.getState().addScore(100);
            this.particleSystem.createExplosion(enemy.getPosition());
            this.audioManager.playExplosion(enemy.getPosition());
            this.postProcessing.createScreenShake(0.4, 150);
            this.cameraController.triggerShake(0.6, 0.3);
            this.environment.createEnvironmentExplosion(enemy.getPosition());
          }
        }
      });
    });
  }
  

  
  private render() {
    this.postProcessing.render();
  }
  
  cleanup() {
    this.stop();
    this.renderer.dispose();
    window.removeEventListener('resize', this.handleResize.bind(this));
    document.removeEventListener('keydown', this.handleKeyDown.bind(this));
    document.removeEventListener('keyup', this.handleKeyUp.bind(this));
  }
  
  resize(width: number, height: number) {
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height);
    this.postProcessing.resize(width, height);
  }
}
