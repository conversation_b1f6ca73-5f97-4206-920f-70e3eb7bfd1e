import * as THREE from 'three';
import { Bullet3D } from './Bullet3D';
import { AudioManager3D } from './AudioManager3D';

export class Player3D {
  private scene: THREE.Scene;
  private camera: THREE.Camera;
  private mesh: THREE.Group;
  private position: THREE.Vector3;
  private velocity: THREE.Vector3;
  private health: number = 100;
  private maxHealth: number = 100;
  
  // Movement
  private moveSpeed: number = 15;
  private keys: { [key: string]: boolean } = {};
  
  // Shooting
  private bullets: Bullet3D[] = [];
  private lastShotTime: number = 0;
  private shotCooldown: number = 150; // milliseconds
  
  // Animation
  private mixer: THREE.AnimationMixer | null = null;
  private walkAction: THREE.AnimationAction | null = null;
  private idleAction: THREE.AnimationAction | null = null;
  private currentAction: THREE.AnimationAction | null = null;
  
  // Weapon
  private weapon: THREE.Group;
  private muzzleFlash: THREE.PointLight;

  // Audio
  private audioManager: AudioManager3D | null = null;
  private lastFootstepTime: number = 0;
  private footstepInterval: number = 500; // milliseconds

  // Camera reference for recoil effects
  private cameraController: any = null;

  constructor(scene: THREE.Scene, camera: THREE.Camera) {
    this.scene = scene;
    this.camera = camera;
    this.position = new THREE.Vector3(0, 0, 0);
    this.velocity = new THREE.Vector3(0, 0, 0);
    
    this.createPlayerModel();
    this.createWeapon();
    this.setupMuzzleFlash();
  }
  
  private createPlayerModel() {
    this.mesh = new THREE.Group();
    
    // Body (torso)
    const bodyGeometry = new THREE.BoxGeometry(1.2, 2, 0.8);
    const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x2E8B57 }); // Military green
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.position.y = 1;
    body.castShadow = true;
    this.mesh.add(body);
    
    // Head
    const headGeometry = new THREE.SphereGeometry(0.4, 16, 16);
    const headMaterial = new THREE.MeshLambertMaterial({ color: 0xFDBCB4 }); // Skin color
    const head = new THREE.Mesh(headGeometry, headMaterial);
    head.position.y = 2.4;
    head.castShadow = true;
    this.mesh.add(head);
    
    // Helmet
    const helmetGeometry = new THREE.SphereGeometry(0.45, 16, 16, 0, Math.PI * 2, 0, Math.PI / 2);
    const helmetMaterial = new THREE.MeshLambertMaterial({ color: 0x556B2F }); // Dark olive
    const helmet = new THREE.Mesh(helmetGeometry, helmetMaterial);
    helmet.position.y = 2.4;
    helmet.castShadow = true;
    this.mesh.add(helmet);
    
    // Arms
    const armGeometry = new THREE.BoxGeometry(0.4, 1.5, 0.4);
    const armMaterial = new THREE.MeshLambertMaterial({ color: 0x2E8B57 });
    
    const leftArm = new THREE.Mesh(armGeometry, armMaterial);
    leftArm.position.set(-0.8, 1, 0);
    leftArm.castShadow = true;
    this.mesh.add(leftArm);
    
    const rightArm = new THREE.Mesh(armGeometry, armMaterial);
    rightArm.position.set(0.8, 1, 0);
    rightArm.castShadow = true;
    this.mesh.add(rightArm);
    
    // Legs
    const legGeometry = new THREE.BoxGeometry(0.4, 1.5, 0.4);
    const legMaterial = new THREE.MeshLambertMaterial({ color: 0x2E8B57 });
    
    const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
    leftLeg.position.set(-0.3, -0.75, 0);
    leftLeg.castShadow = true;
    this.mesh.add(leftLeg);
    
    const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
    rightLeg.position.set(0.3, -0.75, 0);
    rightLeg.castShadow = true;
    this.mesh.add(rightLeg);
    
    // Equipment/backpack
    const backpackGeometry = new THREE.BoxGeometry(0.8, 1, 0.3);
    const backpackMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 }); // Brown
    const backpack = new THREE.Mesh(backpackGeometry, backpackMaterial);
    backpack.position.set(0, 1.2, -0.55);
    backpack.castShadow = true;
    this.mesh.add(backpack);
    
    this.mesh.position.copy(this.position);
    this.scene.add(this.mesh);
  }
  
  private createWeapon() {
    this.weapon = new THREE.Group();
    
    // Rifle body
    const rifleGeometry = new THREE.BoxGeometry(0.1, 0.1, 2);
    const rifleMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
    const rifle = new THREE.Mesh(rifleGeometry, rifleMaterial);
    rifle.castShadow = true;
    this.weapon.add(rifle);
    
    // Rifle stock
    const stockGeometry = new THREE.BoxGeometry(0.15, 0.3, 0.5);
    const stockMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 }); // Brown wood
    const stock = new THREE.Mesh(stockGeometry, stockMaterial);
    stock.position.z = 0.75;
    stock.castShadow = true;
    this.weapon.add(stock);
    
    // Barrel
    const barrelGeometry = new THREE.CylinderGeometry(0.03, 0.03, 0.8, 8);
    const barrelMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });
    const barrel = new THREE.Mesh(barrelGeometry, barrelMaterial);
    barrel.rotation.z = Math.PI / 2;
    barrel.position.z = -0.9;
    barrel.castShadow = true;
    this.weapon.add(barrel);
    
    // Position weapon
    this.weapon.position.set(0.6, 1.5, -0.5);
    this.weapon.rotation.y = -Math.PI / 2;
    this.mesh.add(this.weapon);
  }
  
  private setupMuzzleFlash() {
    this.muzzleFlash = new THREE.PointLight(0xffaa00, 0, 5);
    this.muzzleFlash.position.set(0, 0, -1.3);
    this.weapon.add(this.muzzleFlash);
  }
  
  handleKeyDown(event: KeyboardEvent) {
    this.keys[event.code] = true;
    
    if (event.code === 'Space') {
      event.preventDefault();
      this.shoot();
    }
  }
  
  handleKeyUp(event: KeyboardEvent) {
    this.keys[event.code] = false;
  }
  
  private shoot() {
    const now = Date.now();
    if (now - this.lastShotTime < this.shotCooldown) return;

    this.lastShotTime = now;

    // Create bullet
    const bulletPosition = new THREE.Vector3();
    this.weapon.getWorldPosition(bulletPosition);
    bulletPosition.z -= 1.5; // Offset from barrel

    const bullet = new Bullet3D(this.scene, bulletPosition, new THREE.Vector3(0, 0, -1));
    this.bullets.push(bullet);

    // Muzzle flash effect
    this.muzzleFlash.intensity = 2;
    setTimeout(() => {
      this.muzzleFlash.intensity = 0;
    }, 50);

    // Weapon recoil animation
    this.weapon.position.z += 0.1;
    setTimeout(() => {
      this.weapon.position.z -= 0.1;
    }, 100);

    // Play gunshot sound
    if (this.audioManager) {
      this.audioManager.playGunshot(bulletPosition);
    }

    // Trigger camera recoil
    if (this.cameraController) {
      this.cameraController.triggerRecoil(0.1);
    }
  }
  
  update(deltaTime: number) {
    this.handleMovement(deltaTime);
    this.updateBullets(deltaTime);
    this.updateAnimations(deltaTime);
    
    // Update mesh position
    this.mesh.position.copy(this.position);
  }
  
  private handleMovement(deltaTime: number) {
    this.velocity.set(0, 0, 0);

    // Movement input
    if (this.keys['ArrowLeft'] || this.keys['KeyA']) {
      this.velocity.x = -this.moveSpeed;
    }
    if (this.keys['ArrowRight'] || this.keys['KeyD']) {
      this.velocity.x = this.moveSpeed;
    }
    if (this.keys['ArrowUp'] || this.keys['KeyW']) {
      this.velocity.z = -this.moveSpeed;
    }
    if (this.keys['ArrowDown'] || this.keys['KeyS']) {
      this.velocity.z = this.moveSpeed;
    }

    // Apply movement
    this.position.add(this.velocity.clone().multiplyScalar(deltaTime));

    // Constrain to play area
    this.position.x = Math.max(-25, Math.min(25, this.position.x));
    this.position.z = Math.max(-10, Math.min(10, this.position.z));

    // Rotate player based on movement
    if (this.velocity.length() > 0) {
      const angle = Math.atan2(this.velocity.x, this.velocity.z);
      this.mesh.rotation.y = angle;

      // Play footstep sounds
      const now = Date.now();
      if (now - this.lastFootstepTime > this.footstepInterval && this.audioManager) {
        this.audioManager.playFootstep(this.position);
        this.lastFootstepTime = now;
      }
    }
  }
  
  private updateBullets(deltaTime: number) {
    this.bullets.forEach(bullet => bullet.update(deltaTime));
    this.bullets = this.bullets.filter(bullet => bullet.isActive());
  }
  
  private updateAnimations(deltaTime: number) {
    if (this.mixer) {
      this.mixer.update(deltaTime);
    }
    
    // Simple walking animation (bobbing)
    const isMoving = this.velocity.length() > 0;
    if (isMoving) {
      const time = Date.now() / 500;
      this.mesh.position.y = Math.sin(time) * 0.1;
    } else {
      this.mesh.position.y = 0;
    }
  }
  
  takeDamage(damage: number) {
    this.health = Math.max(0, this.health - damage);
    
    // Screen shake effect (could be implemented in camera)
    // Damage indicator (red flash)
  }
  
  getPosition(): THREE.Vector3 {
    return this.position.clone();
  }
  
  getBullets(): Bullet3D[] {
    return this.bullets;
  }
  
  getHealth(): number {
    return this.health;
  }
  
  getMaxHealth(): number {
    return this.maxHealth;
  }
  
  getStats() {
    return {
      health: this.health,
      maxHealth: this.maxHealth,
      position: this.position.clone()
    };
  }

  setAudioManager(audioManager: AudioManager3D) {
    this.audioManager = audioManager;
  }

  setCameraController(cameraController: any) {
    this.cameraController = cameraController;
  }
}
