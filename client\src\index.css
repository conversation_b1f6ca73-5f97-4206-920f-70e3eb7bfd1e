@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    margin: 0;
    padding: 0;
    overflow: hidden;
    width: 100vw;
    height: 100vh;
  }
}

#root {
  width: 100%;
  height: 100%;
  position: fixed;
}

canvas {
  width: 100% !important;
  height: 100% !important;
  touch-action: none;
}

/* Custom CONTRA Menu Animations */
@keyframes glitch {
  0% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
  100% { transform: translate(0); }
}

@keyframes scanline {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100vh); }
}

@keyframes flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 0, 0, 0.3),
                0 0 40px rgba(255, 0, 0, 0.2),
                0 0 60px rgba(255, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 0, 0, 0.5),
                0 0 60px rgba(255, 0, 0, 0.3),
                0 0 90px rgba(255, 0, 0, 0.2);
  }
}

@keyframes matrix-rain {
  0% { transform: translateY(-100%); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(100vh); opacity: 0; }
}

.glitch-effect {
  animation: glitch 0.3s infinite;
}

.scanline-effect {
  animation: scanline 2s linear infinite;
}

.flicker-effect {
  animation: flicker 0.1s infinite;
}

.pulse-glow-effect {
  animation: pulse-glow 2s ease-in-out infinite;
}

.matrix-rain-effect {
  animation: matrix-rain 3s linear infinite;
}

/* CRT Screen Effect */
.crt-screen {
  position: relative;
}

.crt-screen::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    rgba(0, 255, 0, 0.03) 2px,
    rgba(0, 255, 0, 0.03) 4px
  );
  pointer-events: none;
  z-index: 1000;
}

.crt-screen::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
  pointer-events: none;
  z-index: 1001;
}