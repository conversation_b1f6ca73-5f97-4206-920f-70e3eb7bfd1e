import { useEffect, useState } from 'react';
import { useContraGame } from '../../lib/stores/useContraGame';
import { motion } from 'framer-motion';

const GameMenu = () => {
  const { startGame, score, highScore } = useContraGame();
  const [glitchActive, setGlitchActive] = useState(false);
  const [scanlineOffset, setScanlineOffset] = useState(0);

  // Glitch effect animation
  useEffect(() => {
    const glitchInterval = setInterval(() => {
      if (Math.random() < 0.1) {
        setGlitchActive(true);
        setTimeout(() => setGlitchActive(false), 150);
      }
    }, 2000);
    return () => clearInterval(glitchInterval);
  }, []);

  // Scanline animation
  useEffect(() => {
    const scanlineInterval = setInterval(() => {
      setScanlineOffset(prev => (prev + 1) % 100);
    }, 50);
    return () => clearInterval(scanlineInterval);
  }, []);

  // Particle system with Z-depth
  const particles = Array.from({ length: 50 }, (_, i) => (
    <div
      key={i}
      className="absolute w-1 h-1 rounded-full bg-green-400 opacity-30 animate-pulse"
      style={{
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
        transform: `translateZ(${Math.random() * 50}px)`,
        zIndex: Math.floor(Math.random() * 3),
        filter: 'blur(0.5px)',
        animationDelay: `${Math.random() * 3}s`,
        animationDuration: `${2 + Math.random() * 3}s`
      }}
    />
  ));

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-red-900">
        <div className="absolute inset-0 opacity-10" style={{
          backgroundImage: `
            linear-gradient(rgba(0, 255, 0, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 255, 0, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '20px 20px'
        }} />
        {particles}
        <div
          className="absolute inset-0 pointer-events-none"
          style={{
            background: `repeating-linear-gradient(
              0deg,
              transparent,
              transparent 2px,
              rgba(0, 255, 0, 0.03) 2px,
              rgba(0, 255, 0, 0.03) -1px
            )`,
            transform: `translateY(${scanlineOffset}px) scale(1.02)`,
            borderRadius: '10%',
            filter: 'brightness(0.9) contrast(1.1)'
          }}
        />
      </div>

      {/* Epic Contra Hero Background Image */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Main Hero Image */}
        <div
          className="absolute inset-0 opacity-90"
          style={{
            backgroundImage: 'url(/hero.png)',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            filter: 'brightness(1.3) contrast(1.4) saturate(1.3)',
            mixBlendMode: 'normal'
          }}
        />

        {/* Fallback: IMG tag for better compatibility */}
        <img
          src="/hero.png"
          alt="Contra Heroes"
          className="absolute inset-0 w-full h-full object-cover opacity-90"
          style={{
            filter: 'brightness(1.3) contrast(1.4) saturate(1.3)',
            mixBlendMode: 'normal',
            display: 'none' // Hidden by default, will show if background fails
          }}
          onLoad={(e) => {
            console.log('PNG image loaded successfully!');
            // Hide background div and show img if it loads successfully
            const bgDiv = e.currentTarget.previousElementSibling as HTMLElement;
            if (bgDiv) bgDiv.style.display = 'none';
            e.currentTarget.style.display = 'block';
          }}
          onError={() => {
            console.log('PNG image failed to load, keeping background method...');
            // Keep background div visible if img fails
          }}
        />

        {/* Light overlay gradient to ensure text readability while keeping image bright */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-black/40" />

        {/* Subtle center overlay for menu content area */}
        <div
          className="absolute inset-0"
          style={{
            background: 'radial-gradient(circle at center, transparent 30%, rgba(0,0,0,0.2) 70%, rgba(0,0,0,0.5) 100%)'
          }}
        />
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen p-8">
        <div className="text-center space-y-8 max-w-2xl">
          {/* Title */}
          <div className="relative">
            <motion.h1
              whileHover={{ rotateY: 8, rotateX: 2 }}
              transition={{ type: 'spring', stiffness: 120 }}
              className={`text-8xl font-black text-transparent bg-clip-text bg-gradient-to-r from-red-500 via-orange-500 to-yellow-500 mb-4 tracking-widest font-mono ${glitchActive ? 'animate-pulse' : ''}`}
              style={{
                textShadow: glitchActive
                  ? '2px 0 #ff0000, -2px 0 #00ff00, 0 2px #0000ff'
                  : '2px 2px 0 #ff0000, 4px 4px 0 #00ff00, 6px 6px 0 #0000ff'
              }}
            >
              CONTRA
            </motion.h1>
            {glitchActive && (
              <h1
                className="absolute top-0 left-0 text-8xl font-black text-red-500 mb-4 tracking-widest font-mono opacity-70"
                style={{
                  transform: 'translate(2px, -1px)',
                  clipPath: 'polygon(0 0, 100% 0, 100% 45%, 0 45%)'
                }}
              >
                CONTRA
              </h1>
            )}
          </div>

          {/* Subtitle */}
          <div className="space-y-4">
            <p className="text-2xl text-green-400 font-mono tracking-wider uppercase">Elite Combat Unit</p>
            <p className="text-lg text-gray-300 font-mono">Side-Scrolling Military Action</p>
          </div>

          {/* Stats */}
         <div className="bg-black/40 border-2 border-green-500 outline outline-2 outline-green-300/40 p-6 rounded-xl shadow-lg shadow-green-500/30 transition-all duration-300 hover:shadow-green-500/60">
  <div className="grid grid-cols-2 gap-8 text-green-400 font-mono">
    <div className="text-center">
      <p className="text-xs uppercase tracking-widest mb- text-green-300/80">Current Score</p>
      <p className="text-4xl font-extrabold drop-shadow-md">{score.toLocaleString()}</p>
    </div>
    <div className="text-center">
      <p className="text-xs uppercase tracking-widest mb-2 text-yellow-300/80">High Score</p>
      <p className="text-4xl font-extrabold text-yellow-400 drop-shadow-md">{highScore.toLocaleString()}</p>
    </div>
  </div>
</div>



          {/* Start Button */}
          <div className="space-y-6">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={startGame}
              className="group relative bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white font-bold py-6 px-12 text-2xl font-mono uppercase tracking-wider border-2 border-red-400 hover:border-red-300 shadow-xl"
              style={{
                clipPath: 'polygon(10px 0%, 100% 0%, calc(100% - 10px) 100%, 0% 100%)',
                boxShadow: 'inset -2px -2px 5px rgba(255,255,255,0.1), inset 2px 2px 5px rgba(0,0,0,0.2), 0 0 10px rgba(255,0,0,0.3)'
              }}
            >
              <span className="relative z-10">▶ START MISSION</span>
              <div className="absolute inset-0 bg-gradient-to-r from-red-400 to-red-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300" />
              <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white to-transparent opacity-0 group-hover:opacity-10 transform -skew-x-12 transition-all duration-500" />
            </motion.button>

            {/* Options / Credits */}
            
          </div>

          {/* Controls Info */}
          <div className="bg-black/30 border border-green-500/30 p-6 rounded backdrop-blur-sm">
            <h3 className="text-green-400 font-mono text-lg mb-4 uppercase tracking-wider">Mission Controls</h3>
            <div className="grid grid-cols-2 gap-4 text-gray-300 font-mono text-sm">
              <div className="space-y-2">
                <p><span className="text-green-400">↑↓←→</span> Movement</p>
                <p><span className="text-green-400">SPACE</span> Fire Weapon</p>
              </div>
              <div className="space-y-2">
                <p><span className="text-green-400">ESC</span> Pause Mission</p>
                <p><span className="text-green-400">R</span> Reload</p>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="text-gray-500 font-mono text-xs uppercase tracking-wider">
            <p>Contra Elite Force v2.0</p>
            <p className="text-green-600">Ready for Combat</p>
          </div>
        </div>
      </div>

      {/* Corner Frames */}
      <div className="absolute top-4 left-4 w-16 h-16 border-l-2 border-t-2 border-green-500 opacity-50" />
      <div className="absolute top-4 right-4 w-16 h-16 border-r-2 border-t-2 border-green-500 opacity-50" />
      <div className="absolute bottom-4 left-4 w-16 h-16 border-l-2 border-b-2 border-green-500 opacity-50" />
      <div className="absolute bottom-4 right-4 w-16 h-16 border-r-2 border-b-2 border-green-500 opacity-50" />
    </div>
  );
};

export default GameMenu;
