import { useEffect, useRef } from 'react';
import { useContraGame } from '../../lib/stores/useContraGame';
import { GameEngine } from '../../lib/game/GameEngine';

const GameCanvas = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameEngineRef = useRef<GameEngine | null>(null);
  const { gameState, setGameState } = useContraGame();

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    // Initialize enhanced 2D game engine
    gameEngineRef.current = new GameEngine(ctx, canvas.width, canvas.height);

    // Handle window resize
    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      if (gameEngineRef.current) {
        gameEngineRef.current.resize(canvas.width, canvas.height);
      }
    };

    window.addEventListener('resize', handleResize);

    // Game loop
    let animationId: number;
    const gameLoop = () => {
      if (gameEngineRef.current) {
        if (gameState === 'playing') {
          const shouldContinue = gameEngineRef.current.update();
          if (!shouldContinue) {
            setGameState('gameOver');
          }
        }
        gameEngineRef.current.render();
      }
      animationId = requestAnimationFrame(gameLoop);
    };

    // Start the game loop immediately
    gameLoop();

    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
      if (gameEngineRef.current) {
        gameEngineRef.current.cleanup();
      }
    };
  }, [gameState, setGameState]);

  // Start game when state changes to playing
  useEffect(() => {
    if (gameState === 'playing' && gameEngineRef.current) {
      gameEngineRef.current.start();
    }
  }, [gameState]);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 w-full h-full"
      style={{
        background: 'linear-gradient(to bottom, #4A90E2, #2F4F4F, #8B4513)',
        cursor: 'crosshair'
      }}
    />
  );
};

export default GameCanvas;
