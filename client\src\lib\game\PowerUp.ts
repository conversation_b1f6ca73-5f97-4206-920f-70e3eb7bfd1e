import { PowerUpData, Vector2, PowerUpType } from './types';

export class PowerUp implements PowerUpData {
  position: Vector2;
  velocity: Vector2;
  width: number = 30;
  height: number = 30;
  active: boolean = true;
  type: PowerUpType;
  value: number;
  id: string;
  
  private speed: number = 100;
  private canvasWidth: number;
  private spawnTime: number;

  constructor(
    type: PowerUpType,
    startPosition: Vector2,
    canvasWidth: number
  ) {
    this.type = type;
    this.position = { ...startPosition };
    this.canvasWidth = canvasWidth;
    this.spawnTime = Date.now();
    this.id = Math.random().toString(36).substr(2, 9);

    this.velocity = { x: -this.speed, y: 0 };
    
    // Set value based on type
    this.setupPowerUpType();
  }

  private setupPowerUpType() {
    switch (this.type) {
      case PowerUpType.WEAPON_UPGRADE:
        this.value = 1;
        break;
      case PowerUpType.HEALTH:
        this.value = 50;
        break;
      case PowerUpType.EXTRA_LIFE:
        this.value = 1;
        break;
      case PowerUpType.SCORE_BONUS:
        this.value = 1000;
        break;
    }
  }

  update(deltaTime: number) {
    if (!this.active) return;

    this.position.x += this.velocity.x * deltaTime;

    // Add floating animation
    const time = (Date.now() - this.spawnTime) / 1000;
    this.position.y += Math.sin(time * 3) * 0.5;

    // Deactivate if off screen
    if (this.position.x + this.width < 0) {
      this.active = false;
    }
  }

  render(ctx: CanvasRenderingContext2D) {
    if (!this.active) return;

    // Add glow effect
    ctx.shadowColor = this.getColor();
    ctx.shadowBlur = 10;

    ctx.fillStyle = this.getColor();
    ctx.fillRect(this.position.x, this.position.y, this.width, this.height);

    // Draw icon based on type
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    const centerX = this.position.x + this.width / 2;
    const centerY = this.position.y + this.height / 2 + 6;

    switch (this.type) {
      case PowerUpType.WEAPON_UPGRADE:
        ctx.fillText('W', centerX, centerY);
        break;
      case PowerUpType.HEALTH:
        ctx.fillText('+', centerX, centerY);
        break;
      case PowerUpType.EXTRA_LIFE:
        ctx.fillText('L', centerX, centerY);
        break;
      case PowerUpType.SCORE_BONUS:
        ctx.fillText('S', centerX, centerY);
        break;
    }

    ctx.shadowBlur = 0;
  }

  private getColor(): string {
    switch (this.type) {
      case PowerUpType.WEAPON_UPGRADE:
        return '#FFD700';
      case PowerUpType.HEALTH:
        return '#00FF00';
      case PowerUpType.EXTRA_LIFE:
        return '#FF69B4';
      case PowerUpType.SCORE_BONUS:
        return '#00BFFF';
      default:
        return '#FFFFFF';
    }
  }

  collect(): void {
    this.active = false;
  }

  getPosition(): Vector2 {
    return { ...this.position };
  }

  getBounds() {
    return {
      x: this.position.x,
      y: this.position.y,
      width: this.width,
      height: this.height
    };
  }
}
