import { GameObject, Vector2, PlayerStats, WeaponType } from './types';
import { InputHandler } from './InputHandler';
import { useAudio } from '../stores/useAudio';

export class Player implements GameObject {
  position: Vector2;
  velocity: Vector2;
  width: number = 25; // Smaller for better gameplay
  height: number = 35; // Smaller for better gameplay
  active: boolean = true;
  
  private stats: PlayerStats;
  private inputHandler: InputHandler;
  private speed: number = 300; // pixels per second
  private canvasWidth: number;
  private canvasHeight: number;

  constructor(
    inputHandler: InputHandler, 
    canvasWidth: number, 
    canvasHeight: number
  ) {
    this.inputHandler = inputHandler;
    this.canvasWidth = canvasWidth;
    this.canvasHeight = canvasHeight;
    
    this.position = {
      x: 100,
      y: canvasHeight * 0.85 - this.height // Stand on the metal platform
    };
    
    this.velocity = { x: 0, y: 0 };
    
    this.stats = {
      health: 100,
      maxHealth: 100,
      weaponType: WeaponType.NORMAL,
      fireRate: 300, // ms between shots
      lastShotTime: 0
    };
  }

  update(deltaTime: number) {
    if (!this.active) return;

    this.handleInput(deltaTime);
    this.updatePosition(deltaTime);
    this.constrainToCanvas();
  }

  private handleInput(deltaTime: number) {
    const moveSpeed = this.speed * deltaTime;
    
    this.velocity.x = 0;
    this.velocity.y = 0;

    if (this.inputHandler.isMovingLeft()) {
      this.velocity.x = -moveSpeed;
    }
    if (this.inputHandler.isMovingRight()) {
      this.velocity.x = moveSpeed;
    }
    if (this.inputHandler.isMovingUp()) {
      this.velocity.y = -moveSpeed;
    }
    if (this.inputHandler.isMovingDown()) {
      this.velocity.y = moveSpeed;
    }
  }

  private updatePosition(deltaTime: number) {
    this.position.x += this.velocity.x;
    this.position.y += this.velocity.y;
  }

  private constrainToCanvas() {
    // Keep player within canvas bounds
    this.position.x = Math.max(0, Math.min(this.canvasWidth - this.width, this.position.x));
    this.position.y = Math.max(0, Math.min(this.canvasHeight - this.height, this.position.y));
  }

  canShoot(): boolean {
    const now = Date.now();
    return now - this.stats.lastShotTime >= this.stats.fireRate;
  }

  shoot(): Vector2[] {
    if (!this.canShoot()) return [];

    this.stats.lastShotTime = Date.now();

    // Play shooting sound
    const audioStore = useAudio.getState();
    if (audioStore.hitSound && !audioStore.isMuted) {
      const shootSound = audioStore.hitSound.cloneNode() as HTMLAudioElement;
      shootSound.volume = 0.2;
      shootSound.playbackRate = 1.5; // Higher pitch for gun sound
      shootSound.play().catch(console.log);
    }

    const bulletStartX = this.position.x + this.width;
    const bulletStartY = this.position.y + this.height / 2;

    switch (this.stats.weaponType) {
      case WeaponType.SPREAD:
        return [
          { x: bulletStartX, y: bulletStartY - 10 },
          { x: bulletStartX, y: bulletStartY },
          { x: bulletStartX, y: bulletStartY + 10 }
        ];
      case WeaponType.RAPID:
        return [{ x: bulletStartX, y: bulletStartY }];
      default:
        return [{ x: bulletStartX, y: bulletStartY }];
    }
  }

  takeDamage(damage: number) {
    this.stats.health -= damage;

    // Play damage sound
    const audioStore = useAudio.getState();
    audioStore.playHit();

    if (this.stats.health <= 0) {
      this.stats.health = 0;
      this.active = false;
    }
  }

  heal(amount: number) {
    this.stats.health = Math.min(this.stats.maxHealth, this.stats.health + amount);
  }

  upgradeWeapon(weaponType: WeaponType) {
    this.stats.weaponType = weaponType;
    if (weaponType === WeaponType.RAPID) {
      this.stats.fireRate = 150;
    }
  }

  render(ctx: CanvasRenderingContext2D) {
    if (!this.active) return;

    this.renderCharacter(ctx);
    this.renderHealthBar(ctx);
  }

  private renderCharacter(ctx: CanvasRenderingContext2D) {
    const x = this.position.x;
    const y = this.position.y;
    const w = this.width;
    const h = this.height;
    const centerX = x + w / 2;
    const centerY = y + h / 2;

    // Save context for transformations
    ctx.save();

    // DRAMATIC 3D SHADOW - much larger and more realistic
    ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
    ctx.beginPath();
    ctx.ellipse(centerX + 5, y + h + 8, w * 0.6, 12, 0, 0, Math.PI * 2);
    ctx.fill();

    // MASSIVE BRIGHT OUTLINE for maximum visibility
    ctx.strokeStyle = '#FF0000'; // Bright red outline
    ctx.lineWidth = 4;
    ctx.strokeRect(x - 2, y - 2, w + 4, h + 4);

    // Secondary golden outline
    ctx.strokeStyle = '#FFD700';
    ctx.lineWidth = 2;
    ctx.strokeRect(x, y, w, h);

    // 3D BODY with realistic gradient shading
    const bodyGradient = ctx.createLinearGradient(x, y + 20, x + w, y + h - 20);
    bodyGradient.addColorStop(0, '#1C4A1C'); // Very dark military green
    bodyGradient.addColorStop(0.2, '#2E5D2E'); // Dark green
    bodyGradient.addColorStop(0.5, '#4A7C4A'); // Medium green
    bodyGradient.addColorStop(0.8, '#5E8E5E'); // Light green
    bodyGradient.addColorStop(1, '#2E5D2E'); // Back to dark for depth

    ctx.fillStyle = bodyGradient;
    ctx.fillRect(x + 5, y + 25, w - 10, h - 45);

    // Add body armor/vest with 3D effect
    const armorGradient = ctx.createLinearGradient(x + 8, y + 30, x + w - 8, y + 60);
    armorGradient.addColorStop(0, '#2F2F2F');
    armorGradient.addColorStop(0.5, '#4F4F4F');
    armorGradient.addColorStop(1, '#1F1F1F');
    ctx.fillStyle = armorGradient;
    ctx.fillRect(x + 8, y + 30, w - 16, 30);

    // REALISTIC 3D HEAD with proper shading
    const headGradient = ctx.createRadialGradient(centerX - 3, y + 15, 2, centerX, y + 15, 15);
    headGradient.addColorStop(0, '#FFDBAC'); // Light skin
    headGradient.addColorStop(0.7, '#E6B887'); // Medium skin
    headGradient.addColorStop(1, '#D4A574'); // Darker skin for depth

    ctx.fillStyle = headGradient;
    ctx.beginPath();
    ctx.arc(centerX, y + 15, 15, 0, Math.PI * 2);
    ctx.fill();

    // TACTICAL HELMET with realistic 3D effect
    const helmetGradient = ctx.createLinearGradient(centerX - 18, y - 5, centerX + 18, y + 20);
    helmetGradient.addColorStop(0, '#1A3D1A'); // Very dark green
    helmetGradient.addColorStop(0.3, '#2E5D2E'); // Dark green
    helmetGradient.addColorStop(0.7, '#4A7C4A'); // Medium green
    helmetGradient.addColorStop(1, '#1A3D1A'); // Back to very dark

    ctx.fillStyle = helmetGradient;
    ctx.beginPath();
    ctx.arc(centerX, y + 10, 18, Math.PI, Math.PI * 2);
    ctx.fill();

    // Helmet details and straps
    ctx.fillStyle = '#654321';
    ctx.fillRect(centerX - 20, y + 12, 40, 2); // Chin strap
    ctx.fillStyle = '#333333';
    ctx.fillRect(centerX - 2, y + 2, 4, 6); // Night vision mount

    // DETAILED FACE with realistic features
    // Eyes with depth
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(centerX - 6, y + 12, 4, 3);
    ctx.fillRect(centerX + 2, y + 12, 4, 3);
    ctx.fillStyle = '#000080'; // Blue eyes
    ctx.fillRect(centerX - 5, y + 13, 2, 2);
    ctx.fillRect(centerX + 3, y + 13, 2, 2);

    // Nose and mouth
    ctx.fillStyle = '#D4A574';
    ctx.fillRect(centerX - 1, y + 16, 2, 3);
    ctx.fillStyle = '#8B4513';
    ctx.fillRect(centerX - 2, y + 20, 4, 1);

    // MUSCULAR ARMS with 3D shading
    const armGradient = ctx.createLinearGradient(x, y + 25, x + 15, y + 50);
    armGradient.addColorStop(0, '#4A7C4A');
    armGradient.addColorStop(0.5, '#2E5D2E');
    armGradient.addColorStop(1, '#1C4A1C');

    ctx.fillStyle = armGradient;
    ctx.fillRect(x - 2, y + 25, 12, 25); // Left arm (bigger)
    ctx.fillRect(x + w - 10, y + 25, 12, 25); // Right arm (bigger)

    // REALISTIC HANDS with gloves
    ctx.fillStyle = '#2F2F2F'; // Black tactical gloves
    ctx.fillRect(x - 2, y + 45, 10, 12); // Left hand
    ctx.fillRect(x + w - 8, y + 45, 10, 12); // Right hand

    // POWERFUL LEGS with muscle definition
    const legGradient = ctx.createLinearGradient(x + 8, y + h - 25, x + w - 8, y + h);
    legGradient.addColorStop(0, '#4A7C4A');
    legGradient.addColorStop(0.5, '#2E5D2E');
    legGradient.addColorStop(1, '#1C4A1C');

    ctx.fillStyle = legGradient;
    ctx.fillRect(x + 8, y + h - 25, 12, 25); // Left leg (bigger)
    ctx.fillRect(x + w - 20, y + h - 25, 12, 25); // Right leg (bigger)

    // COMBAT BOOTS with realistic detail
    const bootGradient = ctx.createLinearGradient(x + 5, y + h - 12, x + w - 5, y + h);
    bootGradient.addColorStop(0, '#1C1C1C');
    bootGradient.addColorStop(0.5, '#2F2F2F');
    bootGradient.addColorStop(1, '#0F0F0F');

    ctx.fillStyle = bootGradient;
    ctx.fillRect(x + 5, y + h - 12, 18, 12); // Left boot (much bigger)
    ctx.fillRect(x + w - 23, y + h - 12, 18, 12); // Right boot (much bigger)

    // Boot laces
    ctx.strokeStyle = '#8B4513';
    ctx.lineWidth = 1;
    for (let i = 0; i < 3; i++) {
      ctx.beginPath();
      ctx.moveTo(x + 8, y + h - 10 + i * 3);
      ctx.lineTo(x + 20, y + h - 10 + i * 3);
      ctx.stroke();
      ctx.moveTo(x + w - 20, y + h - 10 + i * 3);
      ctx.lineTo(x + w - 8, y + h - 10 + i * 3);
      ctx.stroke();
    }

    // MASSIVE REALISTIC ASSAULT RIFLE with 3D details
    const weaponGradient = ctx.createLinearGradient(x + w - 10, y + 30, x + w + 25, y + 40);
    weaponGradient.addColorStop(0, '#2F2F2F'); // Dark metal
    weaponGradient.addColorStop(0.3, '#4F4F4F'); // Medium metal
    weaponGradient.addColorStop(0.7, '#6F6F6F'); // Light metal
    weaponGradient.addColorStop(1, '#1F1F1F'); // Very dark for depth

    // Main rifle body
    ctx.fillStyle = weaponGradient;
    ctx.fillRect(x + w - 8, y + 28, 30, 8); // Main body (much longer)

    // Rifle barrel with realistic thickness
    ctx.fillStyle = '#1A1A1A';
    ctx.fillRect(x + w + 15, y + 30, 20, 4); // Long barrel

    // Rifle stock
    ctx.fillStyle = '#654321'; // Wood stock
    ctx.fillRect(x + w - 12, y + 25, 15, 12);

    // Scope/sight
    ctx.fillStyle = '#333333';
    ctx.fillRect(x + w + 5, y + 24, 12, 4);
    ctx.fillStyle = '#4169E1'; // Blue lens
    ctx.fillRect(x + w + 6, y + 25, 2, 2);
    ctx.fillRect(x + w + 14, y + 25, 2, 2);

    // Trigger guard
    ctx.strokeStyle = '#2F2F2F';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.arc(x + w - 5, y + 34, 3, 0, Math.PI);
    ctx.stroke();

    // Magazine
    ctx.fillStyle = '#1F1F1F';
    ctx.fillRect(x + w - 6, y + 36, 8, 15);

    // Muzzle flash effect (if recently fired)
    const timeSinceShot = Date.now() - this.stats.lastShotTime;
    if (timeSinceShot < 100) {
      const flashGradient = ctx.createRadialGradient(x + w + 35, y + 32, 0, x + w + 35, y + 32, 15);
      flashGradient.addColorStop(0, '#FFFF00'); // Bright yellow center
      flashGradient.addColorStop(0.3, '#FF8C00'); // Orange
      flashGradient.addColorStop(0.6, '#FF4500'); // Red-orange
      flashGradient.addColorStop(1, 'rgba(255, 69, 0, 0)'); // Transparent edge

      ctx.fillStyle = flashGradient;
      ctx.beginPath();
      ctx.arc(x + w + 35, y + 32, 15, 0, Math.PI * 2);
      ctx.fill();
    }

    // Add movement animation (realistic soldier movement)
    const time = Date.now() / 150;
    const isMoving = this.velocity.x !== 0 || this.velocity.y !== 0;
    if (isMoving) {
      const bobOffset = Math.sin(time) * 2; // More pronounced movement
      const sway = Math.cos(time * 0.7) * 1;
      ctx.translate(sway, bobOffset);
    }

    ctx.restore();
  }

  private renderHealthBar(ctx: CanvasRenderingContext2D) {
    const barWidth = this.width;
    const barHeight = 4;
    const barX = this.position.x;
    const barY = this.position.y - 10;

    // Background
    ctx.fillStyle = '#FF0000';
    ctx.fillRect(barX, barY, barWidth, barHeight);

    // Health
    const healthPercent = this.stats.health / this.stats.maxHealth;
    ctx.fillStyle = '#00FF00';
    ctx.fillRect(barX, barY, barWidth * healthPercent, barHeight);
  }

  getStats(): PlayerStats {
    return { ...this.stats };
  }

  getPosition(): Vector2 {
    return { ...this.position };
  }

  resize(canvasWidth: number, canvasHeight: number) {
    this.canvasWidth = canvasWidth;
    this.canvasHeight = canvasHeight;
  }

  reset() {
    this.position = {
      x: 100,
      y: this.canvasHeight * 0.85 - this.height // Stand on the metal platform
    };
    this.velocity = { x: 0, y: 0 };
    this.stats = {
      health: 100,
      maxHealth: 100,
      weaponType: WeaponType.NORMAL,
      fireRate: 300,
      lastShotTime: 0
    };
    this.active = true;
  }
}
